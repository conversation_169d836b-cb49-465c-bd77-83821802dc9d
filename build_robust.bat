@echo off
echo ========================================
echo    BF1 Horde Mode - Robust Build
echo ========================================
echo.

REM Clean up any previous build artifacts
if exist "*.obj" del *.obj >nul 2>&1
if exist "*.exe" del *.exe >nul 2>&1

REM Create directories
if not exist "bin" mkdir bin
if not exist "bin\Release" mkdir bin\Release

echo Searching for Visual Studio...

REM Try multiple Visual Studio paths
set "VCVARS_PATH="

REM VS 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo Found VS 2022 Community
    goto :found_vs
)

REM VS 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    echo Found VS 2022 Professional
    goto :found_vs
)

REM VS 2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo Found VS 2019 Community
    goto :found_vs
)

REM VS Build Tools 2022
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    echo Found VS Build Tools 2022
    goto :found_vs
)

REM Check if cl.exe is already in PATH
where cl >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo Visual Studio compiler already in PATH
    goto :compile
)

echo.
echo ERROR: Visual Studio not found!
echo.
echo Please install one of the following:
echo 1. Visual Studio 2022 Community (Free)
echo    Download: https://visualstudio.microsoft.com/vs/community/
echo.
echo 2. Visual Studio Build Tools 2022 (Minimal)
echo    Download: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo.
echo Make sure to install the "C++ build tools" workload.
echo.
echo Alternative: Open BF1HordeMode.sln in Visual Studio and build there.
echo.
pause
exit /b 1

:found_vs
echo Setting up Visual Studio environment...
call "%VCVARS_PATH%"
if %ERRORLEVEL% neq 0 (
    echo Failed to setup Visual Studio environment
    pause
    exit /b 1
)

:compile
echo.
echo ========================================
echo Starting compilation...
echo ========================================

REM Compile with verbose output
cl /nologo /EHsc /std:c++17 /O2 ^
   /I. /Iimgui ^
   /D_CRT_SECURE_NO_WARNINGS /DNOMINMAX /DWIN32_LEAN_AND_MEAN ^
   /Fo"obj\\" ^
   main.cpp ^
   BF1ModGUI.cpp ^
   BF1HordeMode.cpp ^
   BF1HordeModeCore.cpp ^
   imgui\imgui.cpp ^
   imgui\imgui_demo.cpp ^
   imgui\imgui_draw.cpp ^
   imgui\imgui_tables.cpp ^
   imgui\imgui_widgets.cpp ^
   imgui\imgui_impl_win32.cpp ^
   imgui\imgui_impl_dx11.cpp ^
   /link ^
   /OUT:bin\Release\BF1HordeMode.exe ^
   d3d11.lib d3dcompiler.lib xinput.lib user32.lib gdi32.lib shell32.lib ^
   ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Check the error messages above.
    echo Common issues:
    echo 1. Missing Visual Studio C++ tools
    echo 2. Missing Windows SDK
    echo 3. Code compilation errors
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================

REM Copy to root for easy access
copy "bin\Release\BF1HordeMode.exe" "BF1HordeMode.exe" >nul
if exist "BF1HordeMode.exe" (
    echo ✓ Executable created: BF1HordeMode.exe
) else (
    echo ✓ Executable created: bin\Release\BF1HordeMode.exe
)

echo.
echo Ready to launch! You can now:
echo 1. Run: launch.bat
echo 2. Or directly: BF1HordeMode.exe
echo.
echo Press any key to continue...
pause >nul
