#include "BF1HordeModeCore.h"
#include <algorithm>
#include <cmath>
#include <iostream>

std::unique_ptr<BF1HordeModeCore> g_HordeModeCore = nullptr;

BF1HordeModeCore::BF1HordeModeCore() : m_rng(m_rd()) {
    m_gameStartTime = std::chrono::steady_clock::now();
    m_lastUpdate = std::chrono::steady_clock::now();
}

BF1HordeModeCore::~BF1HordeModeCore() {
    Shutdown();
}

bool BF1HordeModeCore::Initialize() {
    std::cout << "Initializing BF1 Horde Mode Core...\n";
    
    // Initialize enemy templates
    InitializeEnemyTemplates();
    
    // Initialize spawn points around the map
    InitializeSpawnPoints();
    
    // Set initial game state
    m_gameState = GameState::MENU;
    
    // Initialize player spawn point (center of map)
    m_playerSpawnPoint = {0.0f, 0.0f, 0.0f};
    m_mapCenter = m_playerSpawnPoint;
    
    std::cout << "Horde Mode Core initialized successfully!\n";
    return true;
}

void BF1HordeModeCore::Shutdown() {
    m_activeEnemies.clear();
    m_activePowerUps.clear();
    m_playerEffects.clear();
}

void BF1HordeModeCore::Update(float deltaTime) {
    auto now = std::chrono::steady_clock::now();
    float dt = std::chrono::duration<float>(now - m_lastUpdate).count();
    m_lastUpdate = now;
    
    // Update survival time
    if (m_gameState == GameState::WAVE_ACTIVE) {
        m_stats.survivalTime += dt;
    }
    
    // Handle controller input
    if (m_controller) {
        HandleControllerInput();
    }
    
    // Update based on game state
    UpdateGameState(dt);
    
    // Update active systems
    if (m_gameState == GameState::WAVE_ACTIVE) {
        UpdateEnemies(dt);
        UpdateSpawnPoints(dt);
        UpdatePowerUps(dt);
        CheckWaveCompletion();
    }
}

void BF1HordeModeCore::StartHordeMode() {
    std::cout << "Starting Horde Mode!\n";
    
    // Reset stats
    m_stats = HordeStats();
    m_stats.currentWave = 1;
    
    // Clear any existing enemies
    m_activeEnemies.clear();
    m_activePowerUps.clear();
    
    // Generate first wave
    GenerateWave(1);
    
    // Set game state
    m_gameState = GameState::PREPARING;
    m_gameStartTime = std::chrono::steady_clock::now();
    
    std::cout << "Horde Mode started! Preparing for Wave 1...\n";
}

void BF1HordeModeCore::StartWave(int waveNumber) {
    std::cout << "Starting Wave " << waveNumber << ": " << m_currentWave.waveTitle << "\n";
    
    m_gameState = GameState::WAVE_ACTIVE;
    m_waveStartTime = std::chrono::steady_clock::now();
    m_lastSpawn = std::chrono::steady_clock::now();
    
    // Reset wave counters
    m_currentWave.enemiesSpawned = 0;
    m_currentWave.enemiesRemaining = m_currentWave.totalEnemies;
}

void BF1HordeModeCore::GenerateWave(int waveNumber) {
    m_currentWave.waveNumber = waveNumber;
    
    // Calculate difficulty scaling
    float difficulty = CalculateDifficulty(waveNumber);
    m_currentWave.difficultyMultiplier = difficulty;
    
    // Determine wave composition
    if (waveNumber <= 5) {
        // Early waves - basic enemies
        m_currentWave.totalEnemies = 8 + (waveNumber * 2);
        m_currentWave.spawnRate = 0.5f; // 2 seconds between spawns
        m_currentWave.enemyTypes = {EnemyType::INFANTRY_BASIC, EnemyType::INFANTRY_BASIC, EnemyType::INFANTRY_VETERAN};
        m_currentWave.waveTitle = "Infantry Assault";
        m_currentWave.waveDescription = "Basic enemy infantry approaching";
    }
    else if (waveNumber <= 10) {
        // Mid-early waves - mixed infantry
        m_currentWave.totalEnemies = 12 + (waveNumber * 2);
        m_currentWave.spawnRate = 0.7f;
        m_currentWave.enemyTypes = {EnemyType::INFANTRY_BASIC, EnemyType::INFANTRY_VETERAN, 
                                   EnemyType::SNIPER, EnemyType::GRENADIER};
        m_currentWave.waveTitle = "Mixed Forces";
        m_currentWave.waveDescription = "Infantry with specialist support";
    }
    else if (waveNumber <= 20) {
        // Mid waves - elite units
        m_currentWave.totalEnemies = 15 + (waveNumber * 2);
        m_currentWave.spawnRate = 1.0f;
        m_currentWave.enemyTypes = {EnemyType::INFANTRY_VETERAN, EnemyType::INFANTRY_ELITE,
                                   EnemyType::MACHINE_GUNNER, EnemyType::FLAMETHROWER};
        m_currentWave.waveTitle = "Elite Forces";
        m_currentWave.waveDescription = "Heavily armed elite soldiers";
    }
    else if (waveNumber % 10 == 0) {
        // Boss waves every 10 waves
        m_currentWave.totalEnemies = 5 + (waveNumber / 2);
        m_currentWave.spawnRate = 0.3f;
        m_currentWave.hasBoss = true;
        m_currentWave.bossType = (waveNumber >= 30) ? EnemyType::BOSS_HEAVY : EnemyType::BOSS_OFFICER;
        m_currentWave.enemyTypes = {EnemyType::INFANTRY_ELITE, m_currentWave.bossType};
        m_currentWave.waveTitle = "BOSS WAVE";
        m_currentWave.waveDescription = "Elite commander with heavy support";
    }
    else {
        // Late game - all enemy types
        m_currentWave.totalEnemies = 20 + (waveNumber * 3);
        m_currentWave.spawnRate = 1.5f;
        m_currentWave.enemyTypes = {EnemyType::INFANTRY_ELITE, EnemyType::MACHINE_GUNNER,
                                   EnemyType::FLAMETHROWER, EnemyType::CAVALRY, EnemyType::TANK_CREW};
        m_currentWave.waveTitle = "Full Assault";
        m_currentWave.waveDescription = "All enemy forces converging";
    }
    
    // Preparation time based on wave difficulty
    m_currentWave.preparationTime = 5.0f + (waveNumber * 0.5f);
    if (m_currentWave.preparationTime > 15.0f) m_currentWave.preparationTime = 15.0f;
    
    std::cout << "Generated Wave " << waveNumber << ": " << m_currentWave.totalEnemies 
              << " enemies, difficulty " << difficulty << "\n";
}

void BF1HordeModeCore::UpdateEnemies(float deltaTime) {
    Vector3 playerPos = GetPlayerPosition();
    
    // Spawn new enemies if needed
    auto now = std::chrono::steady_clock::now();
    float timeSinceLastSpawn = std::chrono::duration<float>(now - m_lastSpawn).count();
    
    if (m_currentWave.enemiesSpawned < m_currentWave.totalEnemies && 
        timeSinceLastSpawn >= (1.0f / m_currentWave.spawnRate)) {
        
        // Select enemy type for this spawn
        EnemyType enemyType = SelectRandomEnemyType(m_currentWave.waveNumber);
        
        // Find best spawn point
        SpawnPoint* spawnPoint = GetBestSpawnPoint(enemyType);
        if (spawnPoint) {
            SpawnEnemy(enemyType, spawnPoint);
            m_currentWave.enemiesSpawned++;
            m_lastSpawn = now;
        }
    }
    
    // Update all active enemies
    for (auto& enemy : m_activeEnemies) {
        if (enemy.isAlive) {
            UpdateEnemyAI(enemy, deltaTime);
        }
    }
    
    // Remove dead enemies
    RemoveDeadEnemies();
}

void BF1HordeModeCore::SpawnEnemy(EnemyType type, SpawnPoint* spawnPoint) {
    // Find enemy template
    auto templateIt = std::find_if(m_enemyTemplates.begin(), m_enemyTemplates.end(),
                                  [type](const EnemyTemplate& t) { return t.type == type; });
    
    if (templateIt == m_enemyTemplates.end()) return;
    
    HordeEnemy enemy;
    enemy.type = type;
    enemy.position = spawnPoint->position;
    enemy.targetPosition = GetPlayerPosition();
    enemy.health = templateIt->health * m_currentWave.difficultyMultiplier * m_settings.enemyHealthMultiplier;
    enemy.maxHealth = enemy.health;
    enemy.speed = templateIt->speed;
    enemy.damage = templateIt->damage * m_currentWave.difficultyMultiplier * m_settings.enemyDamageMultiplier;
    enemy.isAlive = true;
    enemy.isActive = true;
    enemy.spawnPoint = spawnPoint;
    enemy.spawnTime = std::chrono::steady_clock::now();
    enemy.aiState = HordeEnemy::AIState::SPAWNING;
    enemy.aggroLevel = 0.0f;
    
    m_activeEnemies.push_back(enemy);
    m_stats.totalEnemiesSpawned++;
    
    // Update spawn point
    spawnPoint->currentEnemies++;
    spawnPoint->cooldown = 2.0f; // 2 second cooldown
    
    std::cout << "Spawned " << templateIt->name << " at spawn point\n";
}

void BF1HordeModeCore::UpdateEnemyAI(HordeEnemy& enemy, float deltaTime) {
    Vector3 playerPos = GetPlayerPosition();
    enemy.playerDistance = GetDistance(enemy.position, playerPos);
    enemy.hasLineOfSight = HasLineOfSight(enemy.position, playerPos);
    
    // Update AI state based on conditions
    switch (enemy.aiState) {
        case HordeEnemy::AIState::SPAWNING:
            // Brief spawn delay, then move to player
            if (std::chrono::duration<float>(std::chrono::steady_clock::now() - enemy.spawnTime).count() > 1.0f) {
                enemy.aiState = HordeEnemy::AIState::MOVING_TO_PLAYER;
            }
            break;
            
        case HordeEnemy::AIState::MOVING_TO_PLAYER:
            // Move towards player
            enemy.targetPosition = playerPos;
            
            // Switch to attacking if close enough
            if (enemy.playerDistance < 30.0f && enemy.hasLineOfSight) {
                enemy.aiState = HordeEnemy::AIState::ATTACKING;
                enemy.lastAttack = std::chrono::steady_clock::now();
            }
            // Consider flanking if player is far but visible
            else if (enemy.playerDistance > 50.0f && enemy.hasLineOfSight) {
                if (m_rng() % 100 < 30) { // 30% chance to flank
                    enemy.aiState = HordeEnemy::AIState::FLANKING;
                    enemy.targetPosition = FindFlankingPosition(enemy.position, playerPos);
                }
            }
            break;
            
        case HordeEnemy::AIState::ATTACKING:
            // Attack player if in range and has line of sight
            if (enemy.playerDistance < 40.0f && enemy.hasLineOfSight) {
                auto now = std::chrono::steady_clock::now();
                float timeSinceAttack = std::chrono::duration<float>(now - enemy.lastAttack).count();
                
                // Attack based on enemy type (different attack rates)
                float attackRate = (enemy.type == EnemyType::MACHINE_GUNNER) ? 0.1f : 1.0f;
                if (timeSinceAttack >= attackRate) {
                    // Simulate attack (would integrate with BF1's damage system)
                    enemy.lastAttack = now;
                }
            } else {
                // Lost sight or too far, go back to moving
                enemy.aiState = HordeEnemy::AIState::MOVING_TO_PLAYER;
            }
            break;
            
        case HordeEnemy::AIState::FLANKING:
            // Move to flanking position
            if (GetDistance(enemy.position, enemy.targetPosition) < 5.0f) {
                enemy.aiState = HordeEnemy::AIState::MOVING_TO_PLAYER;
            }
            break;
            
        case HordeEnemy::AIState::TAKING_COVER:
            // Take cover behavior (simplified)
            enemy.targetPosition = FindCoverPosition(enemy.position, playerPos);
            if (enemy.health > enemy.maxHealth * 0.5f) {
                enemy.aiState = HordeEnemy::AIState::MOVING_TO_PLAYER;
            }
            break;
    }
    
    // Simulate movement (would integrate with BF1's movement system)
    Vector3 direction = {
        enemy.targetPosition.x - enemy.position.x,
        enemy.targetPosition.y - enemy.position.y,
        enemy.targetPosition.z - enemy.position.z
    };
    
    float distance = sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);
    if (distance > 1.0f) {
        direction.x /= distance;
        direction.y /= distance;
        direction.z /= distance;
        
        enemy.position.x += direction.x * enemy.speed * deltaTime;
        enemy.position.y += direction.y * enemy.speed * deltaTime;
        enemy.position.z += direction.z * enemy.speed * deltaTime;
    }
}

void BF1HordeModeCore::InitializeEnemyTemplates() {
    m_enemyTemplates.clear();
    
    // Basic Infantry
    m_enemyTemplates.push_back({
        EnemyType::INFANTRY_BASIC, "Basic Infantry", 100.0f, 3.0f, 25.0f, 0.6f, 1, 1.0f, 1, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Veteran Infantry
    m_enemyTemplates.push_back({
        EnemyType::INFANTRY_VETERAN, "Veteran Infantry", 150.0f, 3.5f, 35.0f, 0.75f, 1, 0.8f, 3, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Elite Infantry
    m_enemyTemplates.push_back({
        EnemyType::INFANTRY_ELITE, "Elite Infantry", 200.0f, 4.0f, 50.0f, 0.85f, 1, 0.6f, 8, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Sniper
    m_enemyTemplates.push_back({
        EnemyType::SNIPER, "Sniper", 120.0f, 2.5f, 80.0f, 0.95f, 4, 0.3f, 5, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Machine Gunner
    m_enemyTemplates.push_back({
        EnemyType::MACHINE_GUNNER, "Machine Gunner", 180.0f, 2.0f, 40.0f, 0.7f, 3, 0.4f, 7, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Grenadier
    m_enemyTemplates.push_back({
        EnemyType::GRENADIER, "Grenadier", 140.0f, 3.0f, 60.0f, 0.6f, 7, 0.3f, 6, false, {1.0f, 1.0f, 1.0f}
    });
    
    // Flamethrower
    m_enemyTemplates.push_back({
        EnemyType::FLAMETHROWER, "Flamethrower", 220.0f, 2.8f, 70.0f, 0.8f, 8, 0.2f, 10, false, {1.1f, 1.1f, 1.1f}
    });
    
    // Boss Officer
    m_enemyTemplates.push_back({
        EnemyType::BOSS_OFFICER, "Elite Officer", 500.0f, 3.5f, 60.0f, 0.9f, 6, 0.1f, 10, true, {1.2f, 1.2f, 1.2f}
    });
    
    // Boss Heavy
    m_enemyTemplates.push_back({
        EnemyType::BOSS_HEAVY, "Heavy Assault", 800.0f, 2.5f, 100.0f, 0.8f, 3, 0.05f, 20, true, {1.5f, 1.5f, 1.5f}
    });
    
    std::cout << "Initialized " << m_enemyTemplates.size() << " enemy templates\n";
}

void BF1HordeModeCore::InitializeSpawnPoints() {
    m_spawnPoints.clear();

    // Create spawn points in a circle around the map center
    float spawnRadius = m_mapRadius * 0.8f; // Spawn at 80% of map radius
    int numSpawnPoints = 16; // 16 spawn points around the perimeter

    for (int i = 0; i < numSpawnPoints; i++) {
        float angle = (2.0f * 3.14159f * i) / numSpawnPoints;

        SpawnPoint point;
        point.position.x = m_mapCenter.x + cos(angle) * spawnRadius;
        point.position.y = m_mapCenter.y + sin(angle) * spawnRadius;
        point.position.z = m_mapCenter.z;

        // Assign spawn zones based on angle
        if (angle >= -0.39f && angle <= 0.39f) point.zone = SpawnZone::EAST;
        else if (angle >= 0.39f && angle <= 1.18f) point.zone = SpawnZone::NORTHEAST;
        else if (angle >= 1.18f && angle <= 1.96f) point.zone = SpawnZone::NORTH;
        else if (angle >= 1.96f && angle <= 2.75f) point.zone = SpawnZone::NORTHWEST;
        else if (angle >= 2.75f && angle <= 3.53f) point.zone = SpawnZone::WEST;
        else if (angle >= 3.53f && angle <= 4.32f) point.zone = SpawnZone::SOUTHWEST;
        else if (angle >= 4.32f && angle <= 5.11f) point.zone = SpawnZone::SOUTH;
        else point.zone = SpawnZone::SOUTHEAST;

        point.isActive = true;
        point.cooldown = 0.0f;
        point.maxEnemies = 3; // Max 3 enemies per spawn point
        point.currentEnemies = 0;

        m_spawnPoints.push_back(point);
    }

    std::cout << "Initialized " << m_spawnPoints.size() << " spawn points\n";
}

SpawnPoint* BF1HordeModeCore::GetBestSpawnPoint(EnemyType enemyType) {
    Vector3 playerPos = GetPlayerPosition();
    std::vector<SpawnPoint*> availablePoints;

    // Find available spawn points
    for (auto& point : m_spawnPoints) {
        if (CanSpawnAt(&point)) {
            availablePoints.push_back(&point);
        }
    }

    if (availablePoints.empty()) return nullptr;

    // For snipers, prefer distant points
    if (enemyType == EnemyType::SNIPER) {
        auto farthest = std::max_element(availablePoints.begin(), availablePoints.end(),
            [playerPos](SpawnPoint* a, SpawnPoint* b) {
                float distA = sqrt(pow(a->position.x - playerPos.x, 2) + pow(a->position.y - playerPos.y, 2));
                float distB = sqrt(pow(b->position.x - playerPos.x, 2) + pow(b->position.y - playerPos.y, 2));
                return distA < distB;
            });
        return *farthest;
    }

    // For other enemies, prefer points behind player or to the sides
    // Simple implementation: choose random available point
    std::uniform_int_distribution<> dis(0, availablePoints.size() - 1);
    return availablePoints[dis(m_rng)];
}

bool BF1HordeModeCore::CanSpawnAt(SpawnPoint* point) {
    if (!point->isActive) return false;
    if (point->cooldown > 0.0f) return false;
    if (point->currentEnemies >= point->maxEnemies) return false;

    // Check if player is too close to spawn point
    Vector3 playerPos = GetPlayerPosition();
    float distance = GetDistance(point->position, playerPos);
    if (distance < 50.0f) return false; // Don't spawn too close to player

    return true;
}

void BF1HordeModeCore::UpdateSpawnPoints(float deltaTime) {
    for (auto& point : m_spawnPoints) {
        if (point.cooldown > 0.0f) {
            point.cooldown -= deltaTime;
            if (point.cooldown < 0.0f) point.cooldown = 0.0f;
        }

        // Update enemy count at this spawn point
        point.currentEnemies = 0;
        for (const auto& enemy : m_activeEnemies) {
            if (enemy.spawnPoint == &point && enemy.isAlive) {
                point.currentEnemies++;
            }
        }
    }
}

void BF1HordeModeCore::UpdateGameState(float deltaTime) {
    auto now = std::chrono::steady_clock::now();

    switch (m_gameState) {
        case GameState::MENU:
            // Waiting for player to start
            break;

        case GameState::PREPARING:
            {
                float prepTime = std::chrono::duration<float>(now - m_gameStartTime).count();
                if (prepTime >= m_currentWave.preparationTime) {
                    StartWave(m_currentWave.waveNumber);
                }
            }
            break;

        case GameState::WAVE_ACTIVE:
            // Wave is running, enemies are spawning and fighting
            break;

        case GameState::WAVE_COMPLETE:
            {
                // Brief pause between waves
                float pauseTime = std::chrono::duration<float>(now - m_waveStartTime).count();
                if (pauseTime >= 3.0f) { // 3 second pause
                    // Start next wave
                    int nextWave = m_currentWave.waveNumber + 1;
                    GenerateWave(nextWave);
                    m_gameState = GameState::PREPARING;
                    m_gameStartTime = now;
                }
            }
            break;

        case GameState::GAME_OVER:
            // Player died, waiting for restart
            break;

        case GameState::VICTORY:
            // Player completed all waves (unlikely in endless mode)
            break;

        case GameState::PAUSED:
            // Game is paused
            break;
    }
}

void BF1HordeModeCore::CheckWaveCompletion() {
    // Count living enemies
    int livingEnemies = 0;
    for (const auto& enemy : m_activeEnemies) {
        if (enemy.isAlive) livingEnemies++;
    }

    // Wave is complete if all enemies are spawned and all are dead
    if (m_currentWave.enemiesSpawned >= m_currentWave.totalEnemies && livingEnemies == 0) {
        CompleteWave();
    }
}

void BF1HordeModeCore::CompleteWave() {
    std::cout << "Wave " << m_currentWave.waveNumber << " completed!\n";

    m_gameState = GameState::WAVE_COMPLETE;
    m_waveStartTime = std::chrono::steady_clock::now();

    // Award experience
    int waveXP = 50 + (m_currentWave.waveNumber * 10);
    AddExperience(waveXP);

    // Spawn power-ups occasionally
    if (m_settings.enablePowerUps && m_rng() % 100 < 30) { // 30% chance
        Vector3 powerupPos = GetRandomPositionInRadius(m_mapCenter, m_mapRadius * 0.3f);
        PowerUp::Type type = static_cast<PowerUp::Type>(m_rng() % 10);
        SpawnPowerUp(type, powerupPos);
    }

    m_stats.currentWave = m_currentWave.waveNumber + 1;
}

void BF1HordeModeCore::OnEnemyKilled(HordeEnemy& enemy, bool wasHeadshot, bool wasMelee) {
    m_stats.enemiesKilled++;

    if (wasHeadshot) m_stats.headshotKills++;
    if (wasMelee) m_stats.meleeKills++;

    // Award XP based on enemy type
    int xp = 10;
    switch (enemy.type) {
        case EnemyType::INFANTRY_BASIC: xp = 10; break;
        case EnemyType::INFANTRY_VETERAN: xp = 15; break;
        case EnemyType::INFANTRY_ELITE: xp = 25; break;
        case EnemyType::SNIPER: xp = 30; break;
        case EnemyType::MACHINE_GUNNER: xp = 35; break;
        case EnemyType::FLAMETHROWER: xp = 40; break;
        case EnemyType::BOSS_OFFICER: xp = 100; break;
        case EnemyType::BOSS_HEAVY: xp = 200; break;
        default: xp = 10; break;
    }

    if (wasHeadshot) xp *= 2;
    if (wasMelee) xp = static_cast<int>(xp * 1.5f);

    AddExperience(xp);

    // Mark enemy as dead
    enemy.isAlive = false;
    enemy.aiState = HordeEnemy::AIState::DEAD;

    // Update spawn point
    if (enemy.spawnPoint) {
        enemy.spawnPoint->currentEnemies--;
    }
}

void BF1HordeModeCore::AddExperience(int xp) {
    m_stats.experience += xp;

    while (m_stats.experience >= m_stats.experienceToNext) {
        m_stats.experience -= m_stats.experienceToNext;
        LevelUp();
    }
}

void BF1HordeModeCore::LevelUp() {
    m_stats.playerLevel++;
    m_stats.experienceToNext = 100 + (m_stats.playerLevel * 50); // Increasing XP requirements

    std::cout << "Level up! Now level " << m_stats.playerLevel << "\n";

    // Unlock rewards
    UnlockReward(m_stats.playerLevel);
}

void BF1HordeModeCore::UnlockReward(int level) {
    // Simple reward system - could be expanded
    if (level % 5 == 0) {
        std::cout << "Unlocked new weapon attachment at level " << level << "!\n";
    }
    if (level % 10 == 0) {
        std::cout << "Unlocked new weapon at level " << level << "!\n";
    }
}

// Utility functions
float BF1HordeModeCore::GetDistance(Vector3 a, Vector3 b) {
    float dx = a.x - b.x;
    float dy = a.y - b.y;
    float dz = a.z - b.z;
    return sqrt(dx*dx + dy*dy + dz*dz);
}

Vector3 BF1HordeModeCore::GetRandomPositionInRadius(Vector3 center, float radius) {
    std::uniform_real_distribution<float> angleDist(0.0f, 2.0f * 3.14159f);
    std::uniform_real_distribution<float> radiusDist(0.0f, radius);

    float angle = angleDist(m_rng);
    float r = radiusDist(m_rng);

    return {
        center.x + cos(angle) * r,
        center.y + sin(angle) * r,
        center.z
    };
}

EnemyType BF1HordeModeCore::SelectRandomEnemyType(int waveNumber) {
    std::vector<EnemyType> availableTypes;

    // Filter enemy types based on wave number and spawn weights
    for (const auto& enemyTemplate : m_enemyTemplates) {
        if (enemyTemplate.minWave <= waveNumber) {
            // Add multiple entries based on spawn weight
            int weight = static_cast<int>(enemyTemplate.spawnWeight * 100);
            for (int i = 0; i < weight; i++) {
                availableTypes.push_back(enemyTemplate.type);
            }
        }
    }

    if (availableTypes.empty()) return EnemyType::INFANTRY_BASIC;

    std::uniform_int_distribution<> dis(0, availableTypes.size() - 1);
    return availableTypes[dis(m_rng)];
}

float BF1HordeModeCore::CalculateDifficulty(int waveNumber) {
    // Exponential difficulty scaling that levels off
    return 1.0f + (waveNumber * 0.1f) + (waveNumber * waveNumber * 0.01f);
}

Vector3 BF1HordeModeCore::GetPlayerPosition() {
    // This would integrate with BF1's player position system
    // For now, return center of map as placeholder
    return m_playerSpawnPoint;
}

bool BF1HordeModeCore::HasLineOfSight(Vector3 from, Vector3 to) {
    // Simplified line of sight check
    // In a real implementation, this would raycast through the game world
    float distance = GetDistance(from, to);
    return distance < 100.0f; // Assume line of sight within 100 units
}

Vector3 BF1HordeModeCore::FindCoverPosition(Vector3 enemyPos, Vector3 playerPos) {
    // Simple cover finding - move perpendicular to player direction
    Vector3 direction = {playerPos.x - enemyPos.x, playerPos.y - enemyPos.y, 0};
    float length = sqrt(direction.x * direction.x + direction.y * direction.y);

    if (length > 0) {
        direction.x /= length;
        direction.y /= length;

        // Move perpendicular
        return {
            enemyPos.x + direction.y * 20.0f,
            enemyPos.y - direction.x * 20.0f,
            enemyPos.z
        };
    }

    return enemyPos;
}

Vector3 BF1HordeModeCore::FindFlankingPosition(Vector3 enemyPos, Vector3 playerPos) {
    // Simple flanking - move to side of player
    Vector3 direction = {playerPos.x - enemyPos.x, playerPos.y - enemyPos.y, 0};
    float length = sqrt(direction.x * direction.x + direction.y * direction.y);

    if (length > 0) {
        direction.x /= length;
        direction.y /= length;

        // Move to flanking position
        float flankDistance = 30.0f;
        bool leftFlank = (m_rng() % 2) == 0;

        return {
            playerPos.x + (leftFlank ? -direction.y : direction.y) * flankDistance,
            playerPos.y + (leftFlank ? direction.x : -direction.x) * flankDistance,
            playerPos.z
        };
    }

    return enemyPos;
}

void BF1HordeModeCore::RemoveDeadEnemies() {
    m_activeEnemies.erase(
        std::remove_if(m_activeEnemies.begin(), m_activeEnemies.end(),
                      [](const HordeEnemy& enemy) { return !enemy.isAlive; }),
        m_activeEnemies.end()
    );
}
