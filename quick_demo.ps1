# Quick Demo Builder for BF1 Horde Mode
Write-Host "Building BF1 Horde Mode Demo..." -ForegroundColor Green

# Try to find a C++ compiler
$compilers = @(
    "cl.exe",
    "g++.exe", 
    "clang++.exe"
)

$compiler = $null
foreach ($comp in $compilers) {
    if (Get-Command $comp -ErrorAction SilentlyContinue) {
        $compiler = $comp
        Write-Host "Found compiler: $compiler" -ForegroundColor Yellow
        break
    }
}

if (-not $compiler) {
    # Try to setup Visual Studio environment
    $vsPaths = @(
        "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
        "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
        "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    )
    
    foreach ($vsPath in $vsPaths) {
        if (Test-Path $vsPath) {
            Write-Host "Setting up Visual Studio environment..." -ForegroundColor Yellow
            cmd /c "`"$vsPath`" && cl minimal_main.cpp /Fe:BF1HordeDemo.exe"
            if (Test-Path "BF1HordeDemo.exe") {
                Write-Host "✅ Demo compiled successfully!" -ForegroundColor Green
                Write-Host "Run: .\BF1HordeDemo.exe" -ForegroundColor Cyan
                exit 0
            }
        }
    }
}

if ($compiler -eq "cl.exe") {
    # Microsoft Visual C++
    & cl minimal_main.cpp /Fe:BF1HordeDemo.exe
} elseif ($compiler -eq "g++.exe") {
    # GCC
    & g++ -o BF1HordeDemo.exe minimal_main.cpp -std=c++17
} elseif ($compiler -eq "clang++.exe") {
    # Clang
    & clang++ -o BF1HordeDemo.exe minimal_main.cpp -std=c++17
}

if (Test-Path "BF1HordeDemo.exe") {
    Write-Host "✅ Demo compiled successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎮 Ready to run the BF1 Horde Mode Demo!" -ForegroundColor Cyan
    Write-Host "Execute: .\BF1HordeDemo.exe" -ForegroundColor White
    Write-Host ""
    Write-Host "This demo shows the core horde mode systems:" -ForegroundColor Yellow
    Write-Host "• Wave progression system" -ForegroundColor Gray
    Write-Host "• Multiple enemy types" -ForegroundColor Gray  
    Write-Host "• Smart aimbot simulation" -ForegroundColor Gray
    Write-Host "• ESP system simulation" -ForegroundColor Gray
    Write-Host "• Boss battles" -ForegroundColor Gray
    Write-Host "• Statistics tracking" -ForegroundColor Gray
} else {
    Write-Host "❌ Compilation failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "No C++ compiler found. Please install:" -ForegroundColor Yellow
    Write-Host "1. Visual Studio 2022 Community (recommended)" -ForegroundColor Gray
    Write-Host "2. MinGW-w64 with GCC" -ForegroundColor Gray
    Write-Host "3. LLVM with Clang" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Alternative: Open BF1HordeMode.sln in Visual Studio" -ForegroundColor Cyan
}
