cmake_minimum_required(VERSION 3.16)
project(BF1HordeMode)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find required packages
find_package(DirectX QUIET)

# ImGui source files
set(IMGUI_SOURCES
    imgui/imgui.cpp
    imgui/imgui_demo.cpp
    imgui/imgui_draw.cpp
    imgui/imgui_tables.cpp
    imgui/imgui_widgets.cpp
    imgui/imgui_impl_win32.cpp
    imgui/imgui_impl_dx11.cpp
)

# ImGui headers
set(IMGUI_HEADERS
    imgui/imgui.h
    imgui/imgui_impl_win32.h
    imgui/imgui_impl_dx11.h
    imgui/imgui_internal.h
    imgui/imstb_rectpack.h
    imgui/imstb_textedit.h
    imgui/imstb_truetype.h
)

# Project source files
set(PROJECT_SOURCES
    main.cpp
    BF1ModGUI.cpp
    BF1HordeMode.cpp
    BF1HordeModeCore.cpp
)

# Project header files
set(PROJECT_HEADERS
    BF1ModGUI.h
    BF1HordeMode.h
    BF1HordeModeCore.h
)

# Create executable
add_executable(${PROJECT_NAME} 
    ${PROJECT_SOURCES}
    ${PROJECT_HEADERS}
    ${IMGUI_SOURCES}
    ${IMGUI_HEADERS}
)

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/imgui
)

# Link libraries
target_link_libraries(${PROJECT_NAME} PRIVATE
    d3d11
    d3dcompiler
    xinput
    user32
    gdi32
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W3)
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra)
endif()

# Set subsystem to Windows (GUI application)
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )
endif()

# Optional: Create DLL version for injection
option(BUILD_DLL "Build as DLL for injection" OFF)

if(BUILD_DLL)
    add_library(${PROJECT_NAME}_dll SHARED
        ${PROJECT_SOURCES}
        ${PROJECT_HEADERS}
        ${IMGUI_SOURCES}
        ${IMGUI_HEADERS}
    )
    
    target_include_directories(${PROJECT_NAME}_dll PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/imgui
    )
    
    target_link_libraries(${PROJECT_NAME}_dll PRIVATE
        d3d11
        d3dcompiler
        xinput
        user32
        gdi32
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        advapi32
    )
    
    target_compile_definitions(${PROJECT_NAME}_dll PRIVATE
        BUILD_DLL
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
    
    set_target_properties(${PROJECT_NAME}_dll PROPERTIES
        OUTPUT_NAME "BF1HordeMode"
        SUFFIX ".dll"
    )
endif()

# Copy ImGui files if they don't exist
if(NOT EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/imgui/imgui.h")
    message(STATUS "ImGui files not found. Please download ImGui and place in imgui/ directory")
    message(STATUS "Required files:")
    message(STATUS "  - imgui/imgui.h")
    message(STATUS "  - imgui/imgui.cpp")
    message(STATUS "  - imgui/imgui_demo.cpp")
    message(STATUS "  - imgui/imgui_draw.cpp")
    message(STATUS "  - imgui/imgui_tables.cpp")
    message(STATUS "  - imgui/imgui_widgets.cpp")
    message(STATUS "  - imgui/imgui_impl_win32.h")
    message(STATUS "  - imgui/imgui_impl_win32.cpp")
    message(STATUS "  - imgui/imgui_impl_dx11.h")
    message(STATUS "  - imgui/imgui_impl_dx11.cpp")
endif()

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

if(BUILD_DLL)
    install(TARGETS ${PROJECT_NAME}_dll
        RUNTIME DESTINATION bin
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
    )
endif()

# Create documentation
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/README.md.in"
    "${CMAKE_CURRENT_BINARY_DIR}/README.md"
    @ONLY
)

# Print build information
message(STATUS "=== BF1 Horde Mode Build Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Build DLL: ${BUILD_DLL}")
message(STATUS "Output directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
message(STATUS "===========================================")
