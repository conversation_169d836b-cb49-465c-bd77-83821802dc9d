@echo off
echo Downloading ImGui for BF1 Horde Mode...
echo.

REM Create imgui directory if it doesn't exist
if not exist "imgui" mkdir imgui

REM Download ImGui using PowerShell
echo Downloading ImGui v1.90.1...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/ocornut/imgui/archive/refs/tags/v1.90.1.zip' -OutFile 'imgui_temp.zip'}"

if exist "imgui_temp.zip" (
    echo Extracting ImGui...
    powershell -Command "& {Expand-Archive -Path 'imgui_temp.zip' -DestinationPath 'imgui_temp' -Force}"
    
    REM Copy required files to imgui folder
    echo Copying required files...
    copy "imgui_temp\imgui-1.90.1\*.h" "imgui\"
    copy "imgui_temp\imgui-1.90.1\*.cpp" "imgui\"
    copy "imgui_temp\imgui-1.90.1\backends\imgui_impl_win32.*" "imgui\"
    copy "imgui_temp\imgui-1.90.1\backends\imgui_impl_dx11.*" "imgui\"
    
    REM Cleanup
    echo Cleaning up...
    rmdir /s /q "imgui_temp"
    del "imgui_temp.zip"
    
    echo.
    echo ImGui downloaded and extracted successfully!
    echo You can now build the project using CMake.
) else (
    echo Failed to download ImGui. Please download manually from:
    echo https://github.com/ocornut/imgui/releases
    echo.
    echo Required files:
    echo - imgui.h, imgui.cpp
    echo - imgui_demo.cpp, imgui_draw.cpp
    echo - imgui_tables.cpp, imgui_widgets.cpp
    echo - imgui_impl_win32.h, imgui_impl_win32.cpp
    echo - imgui_impl_dx11.h, imgui_impl_dx11.cpp
)

echo.
pause
