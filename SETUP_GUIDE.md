# 🚀 Quick Setup Guide - BF1 Horde Mode

## ⚡ Fast Setup (3 Steps)

### 1. Download ImGui
```bash
# Double-click this file:
download_imgui.bat
```
This will automatically download and extract ImGui to the correct location.

### 2. Build the Project
```bash
# Double-click this file:
build.bat
```
This will compile the entire project using CMake and Visual Studio.

### 3. Launch the Mod
```bash
# Double-click this file:
launch.bat
```
This will start the mod with safety checks and instructions.

## 🎮 Usage

### Getting Started
1. **Launch Battlefield 1** and enter **Horde/Survival mode**
2. **Run launch.bat** (or BF1HordeMode.exe directly)
3. **Press INSERT** or **START button** on Xbox controller
4. **Configure your mods** using the tabbed interface
5. **Have fun!** 🎯

### Xbox Controller Controls
| Button | Action |
|--------|--------|
| **START** | Toggle mod menu |
| **LB/RB** | Switch tabs |
| **D-Pad/Left Stick** | Navigate |
| **A** | Select/Toggle |
| **B** | Back/Cancel |
| **Right Trigger** | Aimbot (when enabled) |

## 📋 Features Overview

### 🎯 Horde Tab
- Real-time wave statistics
- Enemy count and progress
- Quick toggle buttons for common features

### 🎯 Aimbot Tab
- Smart targeting with threat prioritization
- FOV and smoothing controls
- Auto-fire and prediction options
- Bone selection (head/chest/body)

### 👁️ ESP Tab
- Enemy boxes and health bars
- Distance and weapon information
- Threat level color coding
- Adjustable maximum distance

### 🔫 Weapons Tab
- No recoil and spread
- Infinite ammo and rapid fire
- Damage multipliers
- Instant reload

### 🏃 Player Tab
- God mode and speed hacks
- Jump enhancements
- No-clip mode (use carefully!)

### 🎨 Visual Tab
- Custom FOV settings
- Remove flash and smoke effects
- Bright enemy skins
- Menu themes (Dark/Light/Military)

### 🔊 Audio Tab
- Sound ESP for enemy positions
- Ambient noise removal
- Volume controls

## ⚠️ Safety Reminders

### ✅ Safe to Use:
- **Horde/Survival modes**
- **Single-player campaign**
- **Private matches with friends**

### ❌ DO NOT Use:
- **Public multiplayer matches**
- **Ranked/competitive modes**
- **Any online PvP**

**Using this mod in public multiplayer WILL result in permanent bans!**

## 🔧 Troubleshooting

### "ImGui not found"
- Run `download_imgui.bat`
- Or manually download ImGui from GitHub

### "CMake configuration failed"
- Install Visual Studio 2022 with C++ tools
- Or try Visual Studio 2019

### "Could not attach to BF1"
- Make sure BF1 is running
- Run as Administrator
- Check you're in horde mode, not multiplayer

### "Controller not detected"
- Reconnect Xbox controller
- Check Windows Game Controller settings
- Try using keyboard (INSERT key)

### "Menu not appearing"
- Try windowed mode instead of fullscreen
- Press INSERT key instead of controller
- Run as Administrator

## 📁 File Structure
```
BF/
├── imgui/              # ImGui library files
├── BF1HordeMode.exe    # Main executable
├── download_imgui.bat  # ImGui downloader
├── build.bat          # Build script
├── launch.bat         # Launcher with safety checks
├── README.md          # Full documentation
└── bf1_mod_settings.ini # Your saved settings
```

## 🎯 Pro Tips

### For Best Performance:
- Lower ESP distance if experiencing lag
- Disable unused features
- Use windowed mode for better overlay

### For Best Experience:
- Start with Military theme (looks awesome!)
- Enable controller vibration
- Use smooth aimbot settings (5-10)
- Try different FOV values for aimbot

### For Safety:
- Always check you're in horde mode
- Never use in public matches
- Keep settings reasonable to avoid detection

## 🤝 Need Help?

If you run into issues:
1. Check this guide first
2. Read the full README.md
3. Make sure you're following safety guidelines
4. Verify BF1 is in horde/survival mode

---

**Have fun dominating those horde waves! 🎮💥**
