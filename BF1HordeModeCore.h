#pragma once
#include "BF1ModGUI.h"
#include <vector>
#include <unordered_map>
#include <chrono>
#include <random>
#include <queue>

// Forward declarations
struct Vector3 { float x, y, z; };
struct Vector2 { float x, y; };

class BF1HordeModeCore {
public:
    enum class GameState {
        MENU,
        PREPARING,
        WAVE_ACTIVE,
        WAVE_COMPLETE,
        GAME_OVER,
        VICTORY,
        PAUSED
    };

    enum class EnemyType {
        INFANTRY_BASIC,
        INFANTRY_VETERAN,
        INFANTRY_ELITE,
        SNIPER,
        MACHINE_GUNNER,
        GRENADIER,
        FLAMETHROWER,
        CAVALRY,
        TANK_CREW,
        PILOT,
        BOSS_OFFICER,
        BOSS_HEAVY
    };

    enum class SpawnZone {
        NORTH,
        SOUTH,
        EAST,
        WEST,
        NORTHEAST,
        NORTHWEST,
        SOUTHEAST,
        SOUTHWEST
    };

    struct EnemyTemplate {
        EnemyType type;
        std::string name;
        float health;
        float speed;
        float damage;
        float accuracy;
        int weaponType;
        float spawnWeight; // Probability of spawning
        int minWave;       // First wave this enemy appears
        bool isBoss;
        Vector3 scale;     // Size multiplier for bosses
    };

    struct SpawnPoint {
        Vector3 position;
        SpawnZone zone;
        bool isActive;
        float cooldown;
        int maxEnemies;    // Max enemies that can spawn here
        int currentEnemies;
    };

    struct Wave {
        int waveNumber;
        int totalEnemies;
        int enemiesRemaining;
        int enemiesSpawned;
        float spawnRate;           // Enemies per second
        float difficultyMultiplier;
        std::vector<EnemyType> enemyTypes;
        bool hasBoss;
        EnemyType bossType;
        float preparationTime;     // Time before wave starts
        std::string waveTitle;
        std::string waveDescription;
    };

    struct HordeStats {
        int currentWave = 0;
        int totalWaves = 50;       // Endless after wave 50
        int enemiesKilled = 0;
        int totalEnemiesSpawned = 0;
        float survivalTime = 0.0f;
        int playerLevel = 1;
        int experience = 0;
        int experienceToNext = 100;
        float accuracy = 0.0f;
        int headshotKills = 0;
        int meleeKills = 0;
        int explosiveKills = 0;
        float damageDealt = 0.0f;
        float damageTaken = 0.0f;
        int revives = 0;
        int powerupsCollected = 0;
    };

    struct PowerUp {
        enum Type {
            HEALTH_PACK,
            AMMO_CRATE,
            DAMAGE_BOOST,
            SPEED_BOOST,
            INVINCIBILITY,
            EXPLOSIVE_ROUNDS,
            INFINITE_AMMO,
            SLOW_TIME,
            AIRSTRIKE,
            REINFORCEMENTS
        };
        
        Type type;
        Vector3 position;
        float duration;
        float effect;
        bool isActive;
        std::chrono::steady_clock::time_point spawnTime;
    };

    struct HordeEnemy {
        DWORD entityPtr;
        EnemyType type;
        Vector3 position;
        Vector3 targetPosition;
        float health;
        float maxHealth;
        float speed;
        float damage;
        bool isAlive;
        bool isActive;
        SpawnPoint* spawnPoint;
        std::chrono::steady_clock::time_point spawnTime;
        std::chrono::steady_clock::time_point lastAttack;
        
        // AI State
        enum AIState {
            SPAWNING,
            MOVING_TO_PLAYER,
            ATTACKING,
            TAKING_COVER,
            FLANKING,
            RETREATING,
            DEAD
        } aiState;
        
        Vector3 lastKnownPlayerPos;
        float playerDistance;
        bool hasLineOfSight;
        float aggroLevel;
    };

private:
    // Core systems
    GameState m_gameState = GameState::MENU;
    HordeStats m_stats;
    Wave m_currentWave;
    
    // Enemy management
    std::vector<HordeEnemy> m_activeEnemies;
    std::vector<EnemyTemplate> m_enemyTemplates;
    std::vector<SpawnPoint> m_spawnPoints;
    std::queue<EnemyType> m_spawnQueue;
    
    // Power-ups
    std::vector<PowerUp> m_activePowerUps;
    std::vector<PowerUp> m_playerEffects;
    
    // Timing
    std::chrono::steady_clock::time_point m_gameStartTime;
    std::chrono::steady_clock::time_point m_waveStartTime;
    std::chrono::steady_clock::time_point m_lastSpawn;
    std::chrono::steady_clock::time_point m_lastUpdate;
    
    // Game settings
    struct HordeSettings {
        float difficultyScale = 1.0f;
        bool friendlyFire = false;
        bool infiniteAmmo = false;
        bool godMode = false;
        float playerDamageMultiplier = 1.0f;
        float enemyDamageMultiplier = 1.0f;
        float enemyHealthMultiplier = 1.0f;
        float spawnRateMultiplier = 1.0f;
        bool showEnemyHealthBars = true;
        bool showSpawnIndicators = true;
        bool enablePowerUps = true;
        int maxSimultaneousEnemies = 30;
    } m_settings;
    
    // Map data
    Vector3 m_playerSpawnPoint;
    Vector3 m_mapCenter;
    float m_mapRadius = 200.0f;
    
    // Random number generation
    std::random_device m_rd;
    std::mt19937 m_rng;
    
    // Controller integration
    BF1ModGUI::XboxController* m_controller = nullptr;

public:
    BF1HordeModeCore();
    ~BF1HordeModeCore();
    
    // Core lifecycle
    bool Initialize();
    void Shutdown();
    void Update(float deltaTime);
    void Render();
    
    // Game state management
    void StartHordeMode();
    void PauseGame();
    void ResumeGame();
    void EndGame();
    void RestartGame();
    
    // Wave management
    void StartWave(int waveNumber);
    void CompleteWave();
    void GenerateWave(int waveNumber);
    bool IsWaveComplete() const;
    
    // Enemy management
    void SpawnEnemy(EnemyType type, SpawnPoint* spawnPoint);
    void UpdateEnemies(float deltaTime);
    void UpdateEnemyAI(HordeEnemy& enemy, float deltaTime);
    void RemoveDeadEnemies();
    HordeEnemy* FindNearestEnemy(Vector3 position, float maxDistance = 0.0f);
    
    // Spawn system
    void InitializeSpawnPoints();
    SpawnPoint* GetBestSpawnPoint(EnemyType enemyType);
    void UpdateSpawnPoints(float deltaTime);
    bool CanSpawnAt(SpawnPoint* point);
    
    // Power-up system
    void SpawnPowerUp(PowerUp::Type type, Vector3 position);
    void UpdatePowerUps(float deltaTime);
    void CollectPowerUp(PowerUp& powerup);
    void ApplyPowerUpEffect(PowerUp::Type type, float duration, float effect);
    
    // Player interaction
    void OnEnemyKilled(HordeEnemy& enemy, bool wasHeadshot = false, bool wasMelee = false);
    void OnPlayerDamaged(float damage);
    void OnPlayerKilled();
    void RespawnPlayer();
    
    // Progression system
    void AddExperience(int xp);
    void LevelUp();
    void UnlockReward(int level);
    
    // HUD and UI
    void RenderHUD();
    void RenderWaveInfo();
    void RenderEnemyIndicators();
    void RenderPowerUpIndicators();
    void RenderPlayerStats();
    
    // Controller integration
    void HandleControllerInput();
    void SetController(BF1ModGUI::XboxController* controller) { m_controller = controller; }
    
    // Getters
    GameState GetGameState() const { return m_gameState; }
    const HordeStats& GetStats() const { return m_stats; }
    const Wave& GetCurrentWave() const { return m_currentWave; }
    const HordeSettings& GetSettings() const { return m_settings; }
    
    // Setters
    void SetSettings(const HordeSettings& settings) { m_settings = settings; }
    
private:
    // Internal helpers
    void InitializeEnemyTemplates();
    void UpdateGameState(float deltaTime);
    void CheckWaveCompletion();
    void HandlePlayerInput();
    
    // AI helpers
    Vector3 GetPlayerPosition();
    bool HasLineOfSight(Vector3 from, Vector3 to);
    Vector3 FindCoverPosition(Vector3 enemyPos, Vector3 playerPos);
    Vector3 FindFlankingPosition(Vector3 enemyPos, Vector3 playerPos);
    
    // Utility functions
    float GetDistance(Vector3 a, Vector3 b);
    Vector3 GetRandomPositionInRadius(Vector3 center, float radius);
    EnemyType SelectRandomEnemyType(int waveNumber);
    float CalculateDifficulty(int waveNumber);
};

// Global instance
extern std::unique_ptr<BF1HordeModeCore> g_HordeModeCore;
