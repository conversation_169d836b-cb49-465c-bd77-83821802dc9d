@echo off
echo Building BF1 Horde Mode (Simple Build)...
echo.

REM Check if we have Visual Studio Build Tools
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Setting up Visual Studio environment...
    
    REM Try to find Visual Studio
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo.
        echo Visual Studio not found!
        echo Please install Visual Studio 2019 or 2022 with C++ development tools.
        echo.
        echo Download from: https://visualstudio.microsoft.com/downloads/
        echo Make sure to install "Desktop development with C++" workload.
        echo.
        pause
        exit /b 1
    )
)

REM Create output directory
if not exist "bin" mkdir bin
if not exist "bin\Release" mkdir bin\Release

echo.
echo Compiling BF1 Horde Mode...

REM Compile all source files
cl /EHsc /std:c++17 /O2 ^
   /I. /Iimgui ^
   /D_CRT_SECURE_NO_WARNINGS /DNOMINMAX /DWIN32_LEAN_AND_MEAN ^
   main.cpp ^
   BF1ModGUI.cpp ^
   BF1HordeMode.cpp ^
   BF1HordeModeCore.cpp ^
   imgui\imgui.cpp ^
   imgui\imgui_demo.cpp ^
   imgui\imgui_draw.cpp ^
   imgui\imgui_tables.cpp ^
   imgui\imgui_widgets.cpp ^
   imgui\imgui_impl_win32.cpp ^
   imgui\imgui_impl_dx11.cpp ^
   /link ^
   d3d11.lib d3dcompiler.lib xinput.lib user32.lib gdi32.lib shell32.lib ^
   ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib ^
   /OUT:bin\Release\BF1HordeMode.exe

if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Executable created: bin\Release\BF1HordeMode.exe
echo.

REM Copy to root directory for easy access
copy "bin\Release\BF1HordeMode.exe" "BF1HordeMode.exe" >nul
echo Copied executable to root directory for easy access.
echo.

echo Ready to launch! You can now run:
echo   launch.bat
echo or
echo   BF1HordeMode.exe
echo.
pause
