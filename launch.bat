@echo off
title BF1 Horde Mode Launcher
echo.
echo ========================================
echo    BF1 Horde Mode Mod Launcher
echo ========================================
echo.

REM Check if executable exists
if exist "BF1HordeMode.exe" (
    echo Found BF1HordeMode.exe in root directory
    set "EXECUTABLE=BF1HordeMode.exe"
) else if exist "build\bin\Release\BF1HordeMode.exe" (
    echo Found BF1HordeMode.exe in build directory
    set "EXECUTABLE=build\bin\Release\BF1HordeMode.exe"
) else (
    echo Error: BF1HordeMode.exe not found!
    echo.
    echo Please build the project first:
    echo 1. Run download_imgui.bat
    echo 2. Run build.bat
    echo.
    pause
    exit /b 1
)

REM Check if BF1 is running
echo Checking for Battlefield 1 process...
tasklist /FI "IMAGENAME eq bf1.exe" 2>NUL | find /I /N "bf1.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Battlefield 1 is running
) else (
    echo ⚠ Warning: Battlefield 1 not detected
    echo   Make sure BF1 is running before using the mod
)

echo.
echo Instructions:
echo 1. Make sure you're in Horde/Survival mode (NOT multiplayer!)
echo 2. Press INSERT key or START button on controller to toggle menu
echo 3. Use LB/RB to switch tabs, A to select, B to go back
echo 4. Right trigger activates aimbot when enabled
echo.
echo ⚠ IMPORTANT: Only use in Horde Mode or Campaign!
echo   DO NOT use in public multiplayer - you will be banned!
echo.

set /p choice="Press Enter to launch mod, or 'q' to quit: "
if /i "%choice%"=="q" exit /b 0

echo.
echo Launching BF1 Horde Mode Mod...
echo Press Ctrl+C to stop the mod
echo.

REM Launch the mod
"%EXECUTABLE%"

echo.
echo Mod has been closed.
pause
