@echo off
echo Building BF1 Horde Mode Mod...
echo.

REM Check if <PERSON><PERSON><PERSON><PERSON> exists
if not exist "imgui\imgui.h" (
    echo Error: ImGui not found!
    echo Please run download_imgui.bat first or manually download ImGui.
    echo.
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" mkdir build
cd build

REM Configure with CMake
echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo.
    echo CMake configuration failed!
    echo Make sure you have Visual Studio 2022 installed.
    echo You can also try: cmake .. -G "Visual Studio 16 2019" -A x64
    echo.
    pause
    exit /b 1
)

REM Build the project
echo.
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Executable location: build\bin\Release\BF1HordeMode.exe
echo.
echo To run:
echo 1. Launch Battlefield 1
echo 2. Enter horde/survival mode
echo 3. Run BF1HordeMode.exe
echo 4. Press INSERT or START button on controller to open menu
echo.

REM Optional: Copy executable to root directory for easier access
if exist "bin\Release\BF1HordeMode.exe" (
    copy "bin\Release\BF1HordeMode.exe" "..\BF1HordeMode.exe"
    echo Copied executable to root directory for easy access.
    echo.
)

pause
