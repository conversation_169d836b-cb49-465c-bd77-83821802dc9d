// test_gui.cpp - Simple test to verify ImGui + Controller setup works
#include "BF1ModGUI.h"
#include <iostream>

int main() {
    std::cout << "Testing BF1 Mod GUI with Xbox Controller Support...\n";
    
    // Create window class
    WNDCLASSEX wc = { 
        sizeof(WNDCLASSEX), CS_CLASSDC, DefWindowProc, 0L, 0L, 
        GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr,
        L"BF1ModGUITest", nullptr 
    };
    RegisterClassEx(&wc);

    // Create window
    HWND hwnd = CreateWindow(
        wc.lpszClassName, L"BF1 Mod GUI Test", 
        WS_OVERLAPPEDWINDOW, 100, 100, 1280, 800,
        nullptr, nullptr, wc.hInstance, nullptr
    );

    if (!hwnd) {
        std::cout << "Failed to create window!\n";
        return -1;
    }

    // Initialize GUI
    g_ModGUI = std::make_unique<BF1ModGUI>();
    
    if (!g_ModGUI->Initialize(hwnd)) {
        std::cout << "Failed to initialize GUI!\n";
        return -1;
    }

    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);

    std::cout << "GUI initialized successfully!\n";
    std::cout << "Controls:\n";
    std::cout << "- INSERT or START button: Toggle menu\n";
    std::cout << "- LB/RB: Switch tabs\n";
    std::cout << "- D-Pad/Left Stick: Navigate\n";
    std::cout << "- A button: Select/Toggle\n";
    std::cout << "- B button: Back/Cancel\n\n";

    // Force show menu for testing
    g_ModGUI->SetMenuVisible(true);

    // Main message loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Update and render
        g_ModGUI->Update();
        g_ModGUI->Render();
        
        Sleep(16); // ~60 FPS
    }

    // Cleanup
    g_ModGUI->Shutdown();
    g_ModGUI.reset();

    DestroyWindow(hwnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);
    
    return 0;
}
