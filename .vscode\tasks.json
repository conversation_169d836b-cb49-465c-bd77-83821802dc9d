{"version": "2.0.0", "tasks": [{"label": "Build BF1 Horde Mode", "type": "shell", "command": "g++", "args": ["-std=c++17", "-O2", "-I.", "-<PERSON><PERSON><PERSON><PERSON>", "-D_CRT_SECURE_NO_WARNINGS", "-DNOMINMAX", "-DWIN32_LEAN_AND_MEAN", "main.cpp", "BF1ModGUI.cpp", "BF1HordeMode.cpp", "BF1HordeModeCore.cpp", "imgui/imgui.cpp", "imgui/imgui_demo.cpp", "imgui/imgui_draw.cpp", "imgui/imgui_tables.cpp", "imgui/imgui_widgets.cpp", "imgui/imgui_impl_win32.cpp", "imgui/imgui_impl_dx11.cpp", "-o", "BF1HordeMode.exe", "-ld3d11", "-ld3dcompiler", "-lxinput", "-luser32", "-lgdi32", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"]}, {"label": "Build with MSVC", "type": "shell", "command": "cl", "args": ["/EHsc", "/std:c++17", "/O2", "/I.", "/Iim<PERSON>i", "/D_CRT_SECURE_NO_WARNINGS", "/DNOMINMAX", "/DWIN32_LEAN_AND_MEAN", "main.cpp", "BF1ModGUI.cpp", "BF1HordeMode.cpp", "BF1HordeModeCore.cpp", "imgui/imgui.cpp", "imgui/imgui_demo.cpp", "imgui/imgui_draw.cpp", "imgui/imgui_tables.cpp", "imgui/imgui_widgets.cpp", "imgui/imgui_impl_win32.cpp", "imgui/imgui_impl_dx11.cpp", "/link", "d3d11.lib", "d3dcompiler.lib", "xinput.lib", "user32.lib", "gdi32.lib", "shell32.lib", "ole32.lib", "oleaut32.lib", "uuid.lib", "comdlg32.lib", "advapi32.lib", "/OUT:BF1HordeMode.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"]}, {"label": "Build Demo (Simple)", "type": "shell", "command": "g++", "args": ["-std=c++17", "-O2", "minimal_main.cpp", "-o", "BF1HordeDemo.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"]}]}