#include "BF1ModGUI.h"
#include "BF1HordeMode.h"
#include "BF1HordeModeCore.h"
#include <Windows.h>
#include <iostream>
#include <thread>
#include <chrono>

// Window procedure
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    if (g_ModGUI && g_ModGUI->IsInitialized()) {
        // Let ImGui handle the message first
        extern LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
        if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
            return true;
    }

    switch (msg) {
        case WM_SIZE:
            if (g_ModGUI && wParam != SIZE_MINIMIZED) {
                g_ModGUI->CleanupRenderTarget();
                g_ModGUI->CreateRenderTarget();
            }
            return 0;
        case WM_SYSCOMMAND:
            if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
                return 0;
            break;
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}

// Console window for debugging
void CreateConsole() {
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    SetConsoleTitle(L"BF1 Horde Mode - Debug Console");
    
    std::cout << "=== BF1 Horde Mode Mod ===\n";
    std::cout << "Console initialized for debugging\n";
    std::cout << "Press INSERT or START button to toggle menu\n";
    std::cout << "==============================\n\n";
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Create console for debugging
    CreateConsole();
    
    // Create window class
    WNDCLASSEX wc = { 
        sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0L, 0L, 
        GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr,
        L"BF1HordeMod", nullptr 
    };
    RegisterClassEx(&wc);

    // Create window (hidden initially)
    HWND hwnd = CreateWindow(
        wc.lpszClassName, L"BF1 Horde Mode Mod", 
        WS_OVERLAPPEDWINDOW, 100, 100, 1280, 800,
        nullptr, nullptr, wc.hInstance, nullptr
    );

    if (!hwnd) {
        std::cout << "Failed to create window!\n";
        return -1;
    }

    // Initialize global instances
    g_ModGUI = std::make_unique<BF1ModGUI>();
    g_HordeMode = std::make_unique<BF1HordeMode>();
    g_HordeModeCore = std::make_unique<BF1HordeModeCore>();

    // Initialize GUI
    if (!g_ModGUI->Initialize(hwnd)) {
        std::cout << "Failed to initialize GUI!\n";
        return -1;
    }

    // Initialize Custom Horde Mode Core
    std::cout << "Initializing Custom Horde Mode System...\n";
    if (!g_HordeModeCore->Initialize()) {
        std::cout << "Warning: Could not initialize Horde Mode Core.\n";
    } else {
        std::cout << "Custom Horde Mode System initialized successfully!\n";
    }

    // Initialize BF1 Integration (optional)
    std::cout << "Attempting to attach to BF1 process for integration...\n";
    if (!g_HordeMode->Initialize()) {
        std::cout << "Note: Could not attach to BF1 process. Custom horde mode will work standalone.\n";
        std::cout << "For full integration, make sure BF1 is running.\n";
    } else {
        std::cout << "Successfully attached to BF1 for enhanced integration!\n";
    }

    // Link controller between GUI and HordeMode
    // This allows both systems to use the same controller input
    
    ShowWindow(hwnd, SW_HIDE); // Keep window hidden - we'll use overlay
    UpdateWindow(hwnd);

    std::cout << "BF1 Horde Mode Mod is now running!\n";
    std::cout << "Controls:\n";
    std::cout << "- INSERT key or START button: Toggle menu\n";
    std::cout << "- LB/RB: Switch tabs\n";
    std::cout << "- D-Pad/Left Stick: Navigate\n";
    std::cout << "- A button: Select/Toggle\n";
    std::cout << "- B button: Back/Cancel\n";
    std::cout << "- Right Trigger: Aimbot (when enabled)\n\n";

    // Main message loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    
    auto lastUpdate = std::chrono::steady_clock::now();
    
    while (msg.message != WM_QUIT) {
        // Handle Windows messages
        if (PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Limit update rate to ~60 FPS
        auto now = std::chrono::steady_clock::now();
        auto deltaTime = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count();
        
        if (deltaTime < 16) {
            Sleep(1);
            continue;
        }

        // Update systems
        try {
            g_ModGUI->Update();
            g_HordeMode->Update();
            g_HordeModeCore->Update(deltaTime / 1000.0f); // Convert ms to seconds

            // Sync settings between GUI and HordeMode
            if (g_ModGUI->IsMenuVisible()) {
                // Copy GUI settings to HordeMode
                const auto& guiSettings = g_ModGUI->GetSettings();
                BF1HordeMode::HordeSettings hordeSettings;
                
                // Map GUI settings to Horde settings
                hordeSettings.aimbotEnabled = guiSettings.aimbotEnabled;
                hordeSettings.aimbotFOV = guiSettings.aimbotFOV;
                hordeSettings.aimbotSmooth = guiSettings.aimbotSmooth;
                hordeSettings.aimbotBone = guiSettings.aimbotBone;
                hordeSettings.aimbotVisibleOnly = guiSettings.aimbotVisibleOnly;
                hordeSettings.aimbotAutoFire = guiSettings.aimbotAutoFire;
                hordeSettings.aimbotPrediction = guiSettings.aimbotPrediction;
                
                hordeSettings.espEnabled = guiSettings.espEnabled;
                hordeSettings.espBoxes = guiSettings.espBoxes;
                hordeSettings.espNames = guiSettings.espNames;
                hordeSettings.espHealth = guiSettings.espHealth;
                hordeSettings.espDistance = guiSettings.espDistance;
                hordeSettings.espWeapons = guiSettings.espWeapons;
                hordeSettings.espThreatLevel = guiSettings.espThreatLevel;
                hordeSettings.espMaxDistance = guiSettings.espMaxDistance;
                
                hordeSettings.noRecoil = guiSettings.noRecoil;
                hordeSettings.noSpread = guiSettings.noSpread;
                hordeSettings.rapidFire = guiSettings.rapidFire;
                hordeSettings.infiniteAmmo = guiSettings.infiniteAmmo;
                hordeSettings.oneHitKill = guiSettings.oneHitKill;
                hordeSettings.instantReload = guiSettings.instantReload;
                hordeSettings.damageMultiplier = guiSettings.damageMultiplier;
                
                hordeSettings.godMode = guiSettings.godMode;
                hordeSettings.speedHack = guiSettings.speedHack;
                hordeSettings.jumpHack = guiSettings.jumpHack;
                hordeSettings.noClip = guiSettings.noClip;
                hordeSettings.speedMultiplier = guiSettings.speedMultiplier;
                hordeSettings.jumpMultiplier = guiSettings.jumpMultiplier;
                
                hordeSettings.waveSkip = guiSettings.waveSkip;
                hordeSettings.enemyFreeze = guiSettings.enemyFreeze;
                hordeSettings.autoRevive = guiSettings.autoRevive;
                hordeSettings.unlimitedGrenades = guiSettings.unlimitedGrenades;
                
                hordeSettings.removeFlash = guiSettings.removeFlash;
                hordeSettings.removeSmoke = guiSettings.removeSmoke;
                hordeSettings.brightSkins = guiSettings.brightSkins;
                hordeSettings.customFOV = guiSettings.customFOV;
                hordeSettings.fovValue = guiSettings.fovValue;
                hordeSettings.showCrosshair = guiSettings.showCrosshair;
                
                hordeSettings.soundESP = guiSettings.soundESP;
                hordeSettings.removeAmbient = guiSettings.removeAmbient;
                hordeSettings.masterVolume = guiSettings.masterVolume;
                
                g_HordeMode->SetSettings(hordeSettings);
            }
            
            // Render GUI
            g_ModGUI->Render();
            
        } catch (const std::exception& e) {
            std::cout << "Error in main loop: " << e.what() << std::endl;
        }
        
        lastUpdate = now;
    }

    // Cleanup
    std::cout << "Shutting down BF1 Horde Mode Mod...\n";

    g_HordeModeCore->Shutdown();
    g_HordeMode->Shutdown();
    g_ModGUI->Shutdown();

    g_HordeModeCore.reset();
    g_HordeMode.reset();
    g_ModGUI.reset();

    DestroyWindow(hwnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);
    
    FreeConsole();
    
    return 0;
}

// DLL entry point (if building as DLL for injection)
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            // Create thread for mod when injected
            CreateThread(nullptr, 0, (LPTHREAD_START_ROUTINE)WinMain, hModule, 0, nullptr);
            break;
        case DLL_THREAD_ATTACH:
        case DLL_THREAD_DETACH:
        case DLL_PROCESS_DETACH:
            break;
    }
    return TRUE;
}
