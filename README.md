# BF1 Custom Horde Mode

A complete **custom horde mode** created from scratch for Battlefield 1, featuring intelligent AI, progressive waves, boss fights, and full Xbox controller support with an intuitive GUI interface.

## ⚠️ Important Notice

**This is a completely custom horde mode that:**
- **Creates its own game mode** - doesn't modify existing BF1 multiplayer
- **Works standalone** - can run independently of BF1's game modes
- **Integrates with BF1** - uses BF1's engine and assets when available
- **Safe to use** - doesn't interfere with BF1's online systems

**Note:** This creates a new game experience rather than modifying existing modes.

## 🎮 Features

### Complete Custom Horde Mode System
- **Wave-Based Gameplay** - 50+ waves with increasing difficulty and enemy variety
- **Intelligent AI System** - Enemies with different behaviors, flanking, cover-taking
- **Progressive Enemy Types** - From basic infantry to elite bosses with unique abilities
- **Dynamic Spawn System** - Smart enemy spawning around map perimeter with anti-camping
- **Boss Battles** - Special boss waves every 10 waves with unique mechanics
- **Player Progression** - XP system, leveling, unlocks, and persistent stats
- **Power-Up System** - Collectible power-ups with temporary abilities
- **Smart Aimbot** - Prioritizes threats based on distance, weapon type, and health
- **Advanced ESP** - Shows enemy positions, health, weapons, and threat levels
- **Weapon Modifications** - No recoil, infinite ammo, damage multipliers
- **Player Enhancements** - God mode, speed hacks, jump modifications

### Xbox Controller Integration
- **Full controller navigation** through all menus
- **Intuitive button mapping** for quick access to features
- **Haptic feedback** support for enhanced immersion
- **Analog stick precision** for fine-tuned adjustments

### Professional GUI
- **Three visual themes** - Dark, Light, and Military
- **Controller-optimized interface** with large buttons and clear navigation
- **Real-time statistics** showing wave progress and enemy counts
- **Settings persistence** - Your preferences are saved automatically

## 🛠️ Installation

### Prerequisites
1. **Battlefield 1** installed and updated
2. **Visual Studio 2019/2022** with C++ development tools
3. **CMake 3.16+**
4. **DirectX 11 SDK** (usually included with Windows 10/11)

### Building from Source

1. **Clone or download** this repository
2. **Download ImGui** (v1.89+) and extract to `imgui/` folder:
   ```
   BF/
   ├── imgui/
   │   ├── imgui.h
   │   ├── imgui.cpp
   │   ├── imgui_impl_win32.h
   │   ├── imgui_impl_win32.cpp
   │   ├── imgui_impl_dx11.h
   │   ├── imgui_impl_dx11.cpp
   │   └── ... (other ImGui files)
   ├── BF1ModGUI.h
   ├── BF1HordeMode.h
   └── main.cpp
   ```

3. **Build the project**:
   ```bash
   mkdir build
   cd build
   cmake ..
   cmake --build . --config Release
   ```

4. **Run the executable**:
   ```bash
   ./bin/BF1HordeMode.exe
   ```

### Alternative: DLL Injection
Build as DLL for injection into BF1 process:
```bash
cmake -DBUILD_DLL=ON ..
cmake --build . --config Release
```

## 🎯 Usage

### Getting Started
1. **Launch Battlefield 1** and enter a horde/survival mode
2. **Run BF1HordeMode.exe** (or inject the DLL)
3. **Press INSERT** or **START button** on controller to open menu
4. **Configure your desired mods** using the tabbed interface
5. **Close menu** and enjoy enhanced gameplay!

### Controller Controls
| Button | Action |
|--------|--------|
| **START** | Toggle mod menu |
| **LB/RB** | Switch between tabs |
| **D-Pad/Left Stick** | Navigate menu items |
| **A Button** | Select/Toggle options |
| **B Button** | Back/Cancel |
| **Right Trigger** | Activate aimbot (when enabled) |

### Menu Tabs

#### 🎯 Horde Tab
- **Wave Information** - Current wave, enemy count, progress
- **Horde Statistics** - Kills, survival time, efficiency
- **Quick Toggles** - Fast access to common features

#### 🎯 Aimbot Tab
- **Enable/Disable** aimbot functionality
- **FOV Settings** - Adjust targeting field of view
- **Smoothing** - Control aim movement speed
- **Target Selection** - Choose head, chest, or body targeting
- **Auto-Fire** - Automatically shoot at targets
- **Prediction** - Lead moving targets

#### 👁️ ESP Tab
- **Enemy Boxes** - Highlight enemy positions
- **Health Bars** - Show enemy health status
- **Distance Display** - Show range to targets
- **Weapon Information** - Display enemy weapon types
- **Threat Levels** - Color-code enemies by danger level

#### 🔫 Weapons Tab
- **No Recoil** - Eliminate weapon kickback
- **No Spread** - Perfect accuracy
- **Rapid Fire** - Increase fire rate
- **Infinite Ammo** - Never run out of ammunition
- **Damage Multiplier** - Increase weapon damage
- **Instant Reload** - Eliminate reload times

#### 🏃 Player Tab
- **God Mode** - Invincibility
- **Speed Hack** - Move faster than normal
- **Jump Hack** - Enhanced jumping ability
- **No Clip** - Walk through walls (use carefully!)

#### 🎨 Visual Tab
- **Custom FOV** - Adjust field of view
- **Remove Effects** - Eliminate flash/smoke
- **Bright Skins** - Make enemies more visible
- **Crosshair** - Custom crosshair overlay

#### 🔊 Audio Tab
- **Sound ESP** - Audio cues for enemy positions
- **Remove Ambient** - Eliminate background noise
- **Volume Control** - Master volume adjustment

## ⚙️ Configuration

### Settings File
Settings are automatically saved to `bf1_mod_settings.ini` in the same directory as the executable. You can manually edit this file or use the in-game menu.

### Memory Addresses
The mod uses reverse-engineered memory addresses from the BF1 community. If the game updates, you may need to update the offsets in `BF1HordeMode.h`.

## 🔧 Troubleshooting

### Common Issues

**"Could not attach to BF1 process"**
- Make sure Battlefield 1 is running
- Run the mod as Administrator
- Check that BF1 is not running in compatibility mode

**"Controller not detected"**
- Ensure Xbox controller is connected and recognized by Windows
- Try unplugging and reconnecting the controller
- Check Windows Game Controller settings

**"Menu not appearing"**
- Try pressing INSERT key instead of controller START button
- Check if BF1 is in fullscreen mode (try windowed mode)
- Verify the mod is running as Administrator

**"Mods not working"**
- Ensure you're in horde/survival mode, not multiplayer
- Check that the game version matches the mod's memory addresses
- Try restarting both BF1 and the mod

### Performance Tips
- **Lower ESP distance** if experiencing frame drops
- **Disable unused features** to improve performance
- **Use windowed mode** for better overlay compatibility

## 🚨 Safety & Legal

### Anti-Cheat Considerations
- **Only use in horde/survival modes** - never in public multiplayer
- **Private matches only** when playing with others
- **Campaign mode** is completely safe

### Legal Disclaimer
This mod is for educational and entertainment purposes only. Users are responsible for complying with EA's Terms of Service and local laws. The developers are not responsible for any consequences of using this software.

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly in horde mode only
5. Submit a pull request

## 📝 Credits

- **Reverse Engineering Data** - UnknownCheats BF1 community
- **ImGui** - Omar Cornut and contributors
- **DirectX Integration** - Microsoft DirectX SDK
- **Xbox Controller Support** - Microsoft XInput API

## 📄 License

This project is provided as-is for educational purposes. Use at your own risk and responsibility.

---

**Remember: This mod is designed for horde mode fun, not competitive advantage. Play responsibly!** 🎮
