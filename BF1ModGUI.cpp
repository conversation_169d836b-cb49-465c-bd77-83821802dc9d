#include "BF1ModGUI.h"
#include <fstream>
#include <sstream>

std::unique_ptr<BF1ModGUI> g_ModGUI = nullptr;

BF1ModGUI::BF1ModGUI() {
    // Initialize themes
    m_themes.resize(3);
    
    // Dark theme (default)
    m_themes[0] = {
        ImVec4(0.1f, 0.1f, 0.1f, 0.9f),  // background
        ImVec4(0.2f, 0.2f, 0.2f, 1.0f),  // header
        ImVec4(0.3f, 0.3f, 0.3f, 1.0f),  // button
        ImVec4(0.4f, 0.4f, 0.4f, 1.0f),  // button hovered
        ImVec4(0.5f, 0.5f, 0.5f, 1.0f),  // button active
        ImVec4(1.0f, 1.0f, 1.0f, 1.0f),  // text
        ImVec4(0.0f, 0.7f, 1.0f, 1.0f)   // accent (blue)
    };
    
    // Light theme
    m_themes[1] = {
        ImVec4(0.9f, 0.9f, 0.9f, 0.9f),  // background
        ImVec4(0.8f, 0.8f, 0.8f, 1.0f),  // header
        ImVec4(0.7f, 0.7f, 0.7f, 1.0f),  // button
        ImVec4(0.6f, 0.6f, 0.6f, 1.0f),  // button hovered
        ImVec4(0.5f, 0.5f, 0.5f, 1.0f),  // button active
        ImVec4(0.0f, 0.0f, 0.0f, 1.0f),  // text
        ImVec4(0.0f, 0.5f, 1.0f, 1.0f)   // accent
    };
    
    // Military theme
    m_themes[2] = {
        ImVec4(0.15f, 0.2f, 0.1f, 0.9f), // background (dark green)
        ImVec4(0.25f, 0.3f, 0.2f, 1.0f), // header
        ImVec4(0.35f, 0.4f, 0.3f, 1.0f), // button
        ImVec4(0.45f, 0.5f, 0.4f, 1.0f), // button hovered
        ImVec4(0.55f, 0.6f, 0.5f, 1.0f), // button active
        ImVec4(0.9f, 0.9f, 0.8f, 1.0f),  // text (off-white)
        ImVec4(1.0f, 0.6f, 0.0f, 1.0f)   // accent (orange)
    };
}

BF1ModGUI::~BF1ModGUI() {
    Shutdown();
}

bool BF1ModGUI::Initialize(HWND hwnd) {
    g_hWnd = hwnd;
    
    if (!CreateDeviceD3D(hwnd)) {
        CleanupDeviceD3D();
        return false;
    }
    
    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;
    
    // Setup ImGui style
    ImGui::StyleColorsDark();
    ApplyTheme(m_settings.menuTheme);
    
    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);
    
    // Load settings
    LoadSettings();
    
    m_initialized = true;
    return true;
}

void BF1ModGUI::Shutdown() {
    if (!m_initialized) return;
    
    SaveSettings();
    
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
    
    CleanupDeviceD3D();
    m_initialized = false;
}

void BF1ModGUI::Update() {
    if (!m_initialized) return;
    
    UpdateController();
    
    // Toggle menu with INSERT key or controller START button
    static bool lastToggleState = false;
    bool currentToggleState = (GetAsyncKeyState(m_settings.toggleKey) & 0x8000) || 
                             m_controller.IsButtonPressed(XBOX_START);
    
    if (currentToggleState && !lastToggleState) {
        ToggleMenu();
    }
    lastToggleState = currentToggleState;
    
    if (m_showMenu) {
        HandleControllerNavigation();
    }
}

void BF1ModGUI::Render() {
    if (!m_initialized || !m_showMenu) return;
    
    // Start the Dear ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();
    
    RenderMainMenu();
    
    // Rendering
    ImGui::Render();
    const float clear_color_with_alpha[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
    g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
    g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
}

void BF1ModGUI::UpdateController() {
    DWORD currentTime = GetTickCount();
    if (currentTime - m_lastControllerUpdate < 16) return; // ~60 FPS
    
    m_controller.lastState = m_controller.state;
    m_controller.dwResult = XInputGetState(0, &m_controller.state);
    m_controller.connected = (m_controller.dwResult == ERROR_SUCCESS);
    
    m_lastControllerUpdate = currentTime;
}

void BF1ModGUI::HandleControllerNavigation() {
    if (!m_controller.connected) return;
    
    ImGuiIO& io = ImGui::GetIO();
    
    // Tab navigation with shoulder buttons
    if (m_controller.IsButtonPressed(XBOX_LB)) {
        m_selectedTab = (m_selectedTab - 1 + m_tabNames.size()) % m_tabNames.size();
    }
    if (m_controller.IsButtonPressed(XBOX_RB)) {
        m_selectedTab = (m_selectedTab + 1) % m_tabNames.size();
    }
    
    // D-pad navigation
    if (m_controller.IsButtonPressed(XBOX_DPAD_UP)) {
        io.AddKeyEvent(ImGuiKey_UpArrow, true);
    }
    if (m_controller.IsButtonPressed(XBOX_DPAD_DOWN)) {
        io.AddKeyEvent(ImGuiKey_DownArrow, true);
    }
    if (m_controller.IsButtonPressed(XBOX_DPAD_LEFT)) {
        io.AddKeyEvent(ImGuiKey_LeftArrow, true);
    }
    if (m_controller.IsButtonPressed(XBOX_DPAD_RIGHT)) {
        io.AddKeyEvent(ImGuiKey_RightArrow, true);
    }
    
    // A button = Enter/Select
    if (m_controller.IsButtonPressed(XBOX_A)) {
        io.AddKeyEvent(ImGuiKey_Enter, true);
    }
    
    // B button = Back/Cancel
    if (m_controller.IsButtonPressed(XBOX_B)) {
        io.AddKeyEvent(ImGuiKey_Escape, true);
    }
    
    // Analog stick navigation
    float stickX = m_controller.GetLeftStickX();
    float stickY = m_controller.GetLeftStickY();
    
    if (abs(stickX) > 0.3f || abs(stickY) > 0.3f) {
        io.AddKeyAnalogEvent(ImGuiKey_GamepadLStickLeft, stickX < -0.3f, abs(stickX));
        io.AddKeyAnalogEvent(ImGuiKey_GamepadLStickRight, stickX > 0.3f, abs(stickX));
        io.AddKeyAnalogEvent(ImGuiKey_GamepadLStickUp, stickY > 0.3f, abs(stickY));
        io.AddKeyAnalogEvent(ImGuiKey_GamepadLStickDown, stickY < -0.3f, abs(stickY));
    }
}

void BF1ModGUI::RenderMainMenu() {
    ImGuiIO& io = ImGui::GetIO();
    ImVec2 displaySize = io.DisplaySize;
    
    // Set window size and position
    ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
    ImGui::SetNextWindowPos(ImVec2(displaySize.x * 0.5f - 400, displaySize.y * 0.5f - 300), ImGuiCond_FirstUseEver);
    
    // Main window
    ImGui::Begin("BF1 Mod Menu", &m_showMenu, ImGuiWindowFlags_NoCollapse);
    
    // Controller status indicator
    if (m_controller.connected) {
        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Controller Connected");
        ImGui::SameLine();
        ImGui::Text("| LB/RB: Switch Tabs | A: Select | B: Back | START: Toggle Menu");
    } else {
        ImGui::TextColored(ImVec4(1, 1, 0, 1), "Controller Disconnected - Using Keyboard");
    }
    
    ImGui::Separator();
    
    // Tab bar
    if (ImGui::BeginTabBar("ModTabs")) {
        for (int i = 0; i < m_tabNames.size(); i++) {
            bool selected = (i == m_selectedTab);
            if (ImGui::BeginTabItem(m_tabNames[i].c_str(), nullptr, selected ? ImGuiTabItemFlags_SetSelected : 0)) {
                m_selectedTab = i;
                
                switch (i) {
                    case 0: RenderHordeTab(); break;
                    case 1: RenderAimbotTab(); break;
                    case 2: RenderESPTab(); break;
                    case 3: RenderWeaponsTab(); break;
                    case 4: RenderPlayerTab(); break;
                    case 5: RenderVisualTab(); break;
                    case 6: RenderAudioTab(); break;
                    case 7: RenderControllerTab(); break;
                    case 8: RenderAboutTab(); break;
                }
                
                ImGui::EndTabItem();
            }
        }
        ImGui::EndTabBar();
    }
    
    ImGui::End();
}

void BF1ModGUI::ApplyTheme(int themeIndex) {
    if (themeIndex < 0 || themeIndex >= m_themes.size()) return;
    
    ImGuiStyle& style = ImGui::GetStyle();
    Theme& theme = m_themes[themeIndex];
    
    style.Colors[ImGuiCol_WindowBg] = theme.background;
    style.Colors[ImGuiCol_Header] = theme.header;
    style.Colors[ImGuiCol_Button] = theme.button;
    style.Colors[ImGuiCol_ButtonHovered] = theme.buttonHovered;
    style.Colors[ImGuiCol_ButtonActive] = theme.buttonActive;
    style.Colors[ImGuiCol_Text] = theme.text;
    style.Colors[ImGuiCol_CheckMark] = theme.accent;
    style.Colors[ImGuiCol_SliderGrab] = theme.accent;
    style.Colors[ImGuiCol_SliderGrabActive] = theme.accent;
    
    // Make UI more controller-friendly
    style.FramePadding = ImVec2(8, 6);
    style.ItemSpacing = ImVec2(8, 8);
    style.WindowPadding = ImVec2(MENU_PADDING, MENU_PADDING);
    style.FrameRounding = 4.0f;
    style.WindowRounding = 8.0f;
}

bool BF1ModGUI::CreateDeviceD3D(HWND hWnd) {
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    
    HRESULT res = D3D11CreateDeviceAndSwapChain(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags,
        featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain,
        &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    
    if (res != S_OK) return false;

    CreateRenderTarget();
    return true;
}

void BF1ModGUI::CleanupDeviceD3D() {
    CleanupRenderTarget();
    if (g_pSwapChain) { g_pSwapChain->Release(); g_pSwapChain = nullptr; }
    if (g_pd3dDeviceContext) { g_pd3dDeviceContext->Release(); g_pd3dDeviceContext = nullptr; }
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = nullptr; }
}

void BF1ModGUI::CreateRenderTarget() {
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();
}

void BF1ModGUI::CleanupRenderTarget() {
    if (g_mainRenderTargetView) { g_mainRenderTargetView->Release(); g_mainRenderTargetView = nullptr; }
}

void BF1ModGUI::RenderHordeTab() {
    ImGui::Text("Horde Mode Dashboard");
    ImGui::Separator();

    // Horde statistics (placeholder - would be populated by BF1HordeMode)
    ImGui::Text("Current Wave: 5");
    ImGui::Text("Enemies Remaining: 12");
    ImGui::Text("Total Kills: 47");
    ImGui::Text("Wave Progress: 75%%");

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Text("Quick Settings");

    // Quick toggles for common horde features
    if (ImGui::Button("God Mode", CONTROLLER_BUTTON_SIZE)) {
        m_settings.godMode = !m_settings.godMode;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.godMode ? "ON" : "OFF");

    if (ImGui::Button("Infinite Ammo", CONTROLLER_BUTTON_SIZE)) {
        m_settings.infiniteAmmo = !m_settings.infiniteAmmo;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.infiniteAmmo ? "ON" : "OFF");

    if (ImGui::Button("Aimbot", CONTROLLER_BUTTON_SIZE)) {
        m_settings.aimbotEnabled = !m_settings.aimbotEnabled;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.aimbotEnabled ? "ON" : "OFF");

    if (ImGui::Button("ESP", CONTROLLER_BUTTON_SIZE)) {
        m_settings.espEnabled = !m_settings.espEnabled;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.espEnabled ? "ON" : "OFF");

    ImGui::Spacing();
    ImGui::TextColored(ImVec4(1, 1, 0, 1), "Horde Mode Optimized for Xbox Controller");
}

void BF1ModGUI::RenderAimbotTab() {
    ImGui::Text("Aimbot Settings");
    ImGui::Separator();

    // Large controller-friendly checkboxes and sliders
    ImGui::Checkbox("Enable Aimbot", &m_settings.aimbotEnabled);

    if (m_settings.aimbotEnabled) {
        ImGui::Spacing();

        ImGui::Text("Field of View");
        ImGui::SliderFloat("##AimbotFOV", &m_settings.aimbotFOV, 10.0f, 180.0f, "%.1f°");

        ImGui::Text("Smoothing");
        ImGui::SliderFloat("##AimbotSmooth", &m_settings.aimbotSmooth, 1.0f, 20.0f, "%.1f");

        ImGui::Text("Target Bone");
        const char* bones[] = { "Head", "Chest", "Body" };
        ImGui::Combo("##AimbotBone", &m_settings.aimbotBone, bones, 3);

        ImGui::Checkbox("Visible Only", &m_settings.aimbotVisibleOnly);
        ImGui::Checkbox("Auto Fire", &m_settings.aimbotAutoFire);
        ImGui::Checkbox("Prediction", &m_settings.aimbotPrediction);

        ImGui::Spacing();
        ImGui::TextColored(ImVec4(1, 1, 0, 1), "Controller: Right Trigger to Aim");
    }
}

void BF1ModGUI::RenderESPTab() {
    ImGui::Text("ESP Settings");
    ImGui::Separator();

    ImGui::Checkbox("Enable ESP", &m_settings.espEnabled);

    if (m_settings.espEnabled) {
        ImGui::Spacing();

        ImGui::Checkbox("Show Boxes", &m_settings.espBoxes);
        ImGui::Checkbox("Show Names", &m_settings.espNames);
        ImGui::Checkbox("Show Health", &m_settings.espHealth);
        ImGui::Checkbox("Show Distance", &m_settings.espDistance);
        ImGui::Checkbox("Show Weapons", &m_settings.espWeapons);
        ImGui::Checkbox("Show Threat Level", &m_settings.espThreatLevel);

        ImGui::Text("Max Distance");
        ImGui::SliderFloat("##ESPDistance", &m_settings.espMaxDistance, 50.0f, 500.0f, "%.0fm");

        ImGui::Spacing();
        ImGui::Text("Threat Level Colors:");
        ImGui::BulletText("Green: Low Threat");
        ImGui::BulletText("Yellow: Medium Threat");
        ImGui::BulletText("Orange: High Threat");
        ImGui::BulletText("Red: Critical Threat");
    }
}

void BF1ModGUI::RenderWeaponsTab() {
    ImGui::Text("Weapon Modifications");
    ImGui::Separator();

    if (ImGui::Button("No Recoil", CONTROLLER_BUTTON_SIZE)) {
        m_settings.noRecoil = !m_settings.noRecoil;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.noRecoil ? "ON" : "OFF");

    if (ImGui::Button("No Spread", CONTROLLER_BUTTON_SIZE)) {
        m_settings.noSpread = !m_settings.noSpread;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.noSpread ? "ON" : "OFF");

    if (ImGui::Button("Rapid Fire", CONTROLLER_BUTTON_SIZE)) {
        m_settings.rapidFire = !m_settings.rapidFire;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.rapidFire ? "ON" : "OFF");

    if (ImGui::Button("Infinite Ammo", CONTROLLER_BUTTON_SIZE)) {
        m_settings.infiniteAmmo = !m_settings.infiniteAmmo;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.infiniteAmmo ? "ON" : "OFF");

    if (ImGui::Button("One Hit Kill", CONTROLLER_BUTTON_SIZE)) {
        m_settings.oneHitKill = !m_settings.oneHitKill;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.oneHitKill ? "ON" : "OFF");

    if (ImGui::Button("Instant Reload", CONTROLLER_BUTTON_SIZE)) {
        m_settings.instantReload = !m_settings.instantReload;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.instantReload ? "ON" : "OFF");

    ImGui::Spacing();
    ImGui::Text("Damage Multiplier");
    ImGui::SliderFloat("##DamageMultiplier", &m_settings.damageMultiplier, 1.0f, 10.0f, "%.1fx");
}

void BF1ModGUI::RenderPlayerTab() {
    ImGui::Text("Player Modifications");
    ImGui::Separator();

    if (ImGui::Button("God Mode", CONTROLLER_BUTTON_SIZE)) {
        m_settings.godMode = !m_settings.godMode;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.godMode ? "ON" : "OFF");

    if (ImGui::Button("Speed Hack", CONTROLLER_BUTTON_SIZE)) {
        m_settings.speedHack = !m_settings.speedHack;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.speedHack ? "ON" : "OFF");

    if (m_settings.speedHack) {
        ImGui::Text("Speed Multiplier");
        ImGui::SliderFloat("##SpeedMultiplier", &m_settings.speedMultiplier, 1.0f, 5.0f, "%.1fx");
    }

    if (ImGui::Button("Jump Hack", CONTROLLER_BUTTON_SIZE)) {
        m_settings.jumpHack = !m_settings.jumpHack;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.jumpHack ? "ON" : "OFF");

    if (m_settings.jumpHack) {
        ImGui::Text("Jump Multiplier");
        ImGui::SliderFloat("##JumpMultiplier", &m_settings.jumpMultiplier, 1.0f, 5.0f, "%.1fx");
    }

    if (ImGui::Button("No Clip", CONTROLLER_BUTTON_SIZE)) {
        m_settings.noClip = !m_settings.noClip;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.noClip ? "ON" : "OFF");

    ImGui::Spacing();
    ImGui::TextColored(ImVec4(1, 0, 0, 1), "Warning: Use No Clip carefully!");
}

void BF1ModGUI::RenderVisualTab() {
    ImGui::Text("Visual Settings");
    ImGui::Separator();

    ImGui::Text("Menu Opacity");
    ImGui::SliderFloat("##MenuOpacity", &m_settings.menuOpacity, 0.3f, 1.0f, "%.2f");

    ImGui::Text("Menu Theme");
    const char* themes[] = { "Dark", "Light", "Military" };
    if (ImGui::Combo("##MenuTheme", &m_settings.menuTheme, themes, 3)) {
        ApplyTheme(m_settings.menuTheme);
    }

    ImGui::Checkbox("Show FPS", &m_settings.showFPS);

    if (m_settings.showFPS) {
        ImGui::SameLine();
        ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
    }

    ImGui::Separator();
    ImGui::Text("Game Visual Mods");

    ImGui::Checkbox("Custom FOV", &m_settings.customFOV);
    if (m_settings.customFOV) {
        ImGui::Text("FOV Value");
        ImGui::SliderFloat("##FOVValue", &m_settings.fovValue, 60.0f, 120.0f, "%.0f°");
    }

    ImGui::Checkbox("Remove Flash", &m_settings.removeFlash);
    ImGui::Checkbox("Remove Smoke", &m_settings.removeSmoke);
    ImGui::Checkbox("Bright Skins", &m_settings.brightSkins);
    ImGui::Checkbox("Show Crosshair", &m_settings.showCrosshair);
}

void BF1ModGUI::RenderAudioTab() {
    ImGui::Text("Audio Settings");
    ImGui::Separator();

    ImGui::Checkbox("Sound ESP", &m_settings.soundESP);
    if (m_settings.soundESP) {
        ImGui::Text("Audio cues for enemy positions");
    }

    ImGui::Checkbox("Remove Ambient", &m_settings.removeAmbient);
    if (m_settings.removeAmbient) {
        ImGui::Text("Removes background noise for better enemy audio");
    }

    ImGui::Spacing();
    ImGui::Text("Master Volume");
    ImGui::SliderFloat("##MasterVolume", &m_settings.masterVolume, 0.0f, 2.0f, "%.2f");

    ImGui::Spacing();
    ImGui::Text("Audio Features:");
    ImGui::BulletText("Sound ESP highlights enemy footsteps");
    ImGui::BulletText("Ambient removal improves directional audio");
    ImGui::BulletText("Volume boost helps hear distant enemies");
}

void BF1ModGUI::RenderControllerTab() {
    ImGui::Text("Controller Settings");
    ImGui::Separator();

    // Controller status
    if (m_controller.connected) {
        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Controller Status: Connected");

        // Show controller input values
        ImGui::Text("Left Stick: %.2f, %.2f", m_controller.GetLeftStickX(), m_controller.GetLeftStickY());
        ImGui::Text("Right Stick: %.2f, %.2f", m_controller.GetRightStickX(), m_controller.GetRightStickY());
        ImGui::Text("Triggers: L=%.2f R=%.2f", m_controller.GetLeftTrigger(), m_controller.GetRightTrigger());

        // Button states
        ImGui::Text("Buttons: ");
        ImGui::SameLine();
        if (m_controller.IsButtonHeld(XBOX_A)) ImGui::Text("A ");
        if (m_controller.IsButtonHeld(XBOX_B)) ImGui::Text("B ");
        if (m_controller.IsButtonHeld(XBOX_X)) ImGui::Text("X ");
        if (m_controller.IsButtonHeld(XBOX_Y)) ImGui::Text("Y ");

    } else {
        ImGui::TextColored(ImVec4(1, 0, 0, 1), "Controller Status: Disconnected");
    }

    ImGui::Separator();

    ImGui::Text("Controller Sensitivity");
    ImGui::SliderFloat("##ControllerSens", &m_settings.controllerSensitivity, 0.1f, 3.0f, "%.2f");

    ImGui::Checkbox("Controller Vibration", &m_settings.controllerVibration);

    ImGui::Text("Toggle Menu Key");
    ImGui::Text("Current: INSERT (or START button)");

    ImGui::Spacing();
    ImGui::Text("Controller Layout:");
    ImGui::BulletText("START: Toggle Menu");
    ImGui::BulletText("LB/RB: Switch Tabs");
    ImGui::BulletText("D-Pad/Left Stick: Navigate");
    ImGui::BulletText("A: Select/Toggle");
    ImGui::BulletText("B: Back/Cancel");
    ImGui::BulletText("Right Trigger: Aimbot");
}

void BF1ModGUI::RenderAboutTab() {
    ImGui::Text("BF1 Horde Mode Mod v1.0");
    ImGui::Separator();

    ImGui::Text("Features:");
    ImGui::BulletText("Xbox Controller Support");
    ImGui::BulletText("Smart Aimbot with Threat Prioritization");
    ImGui::BulletText("Advanced ESP with Threat Levels");
    ImGui::BulletText("Comprehensive Weapon Modifications");
    ImGui::BulletText("Player Enhancement Suite");
    ImGui::BulletText("Horde-Specific Optimizations");
    ImGui::BulletText("Visual and Audio Improvements");

    ImGui::Spacing();
    ImGui::Text("Controls:");
    ImGui::BulletText("START: Toggle Menu");
    ImGui::BulletText("LB/RB: Switch Tabs");
    ImGui::BulletText("D-Pad/Left Stick: Navigate");
    ImGui::BulletText("A: Select/Toggle");
    ImGui::BulletText("B: Back/Cancel");
    ImGui::BulletText("Right Trigger: Aimbot");

    ImGui::Spacing();
    ImGui::TextColored(ImVec4(1, 1, 0, 1), "For Horde Mode / Campaign Use Only");
    ImGui::TextColored(ImVec4(1, 0, 0, 1), "DO NOT use in public multiplayer!");

    ImGui::Spacing();
    ImGui::Text("Based on reverse engineering data from:");
    ImGui::Text("UnknownCheats BF1 Community");
}

void BF1ModGUI::RenderAimbotTab() {
    ImGui::Text("Aimbot Settings");
    ImGui::Separator();

    // Large controller-friendly checkboxes and sliders
    ImGui::Checkbox("Enable Aimbot", &m_settings.aimbotEnabled);

    if (m_settings.aimbotEnabled) {
        ImGui::Spacing();

        ImGui::Text("Field of View");
        ImGui::SliderFloat("##AimbotFOV", &m_settings.aimbotFOV, 10.0f, 180.0f, "%.1f°");

        ImGui::Text("Smoothing");
        ImGui::SliderFloat("##AimbotSmooth", &m_settings.aimbotSmooth, 1.0f, 20.0f, "%.1f");

        ImGui::Text("Target Bone");
        const char* bones[] = { "Head", "Chest", "Body" };
        ImGui::Combo("##AimbotBone", &m_settings.aimbotBone, bones, 3);

        ImGui::Checkbox("Visible Only", &m_settings.aimbotVisibleOnly);

        ImGui::Spacing();
        ImGui::TextColored(ImVec4(1, 1, 0, 1), "Controller: Right Trigger to Aim");
    }
}

void BF1ModGUI::RenderESPTab() {
    ImGui::Text("ESP Settings");
    ImGui::Separator();

    ImGui::Checkbox("Enable ESP", &m_settings.espEnabled);

    if (m_settings.espEnabled) {
        ImGui::Spacing();

        ImGui::Checkbox("Show Boxes", &m_settings.espBoxes);
        ImGui::Checkbox("Show Names", &m_settings.espNames);
        ImGui::Checkbox("Show Health", &m_settings.espHealth);
        ImGui::Checkbox("Show Distance", &m_settings.espDistance);

        ImGui::Text("Max Distance");
        ImGui::SliderFloat("##ESPDistance", &m_settings.espMaxDistance, 50.0f, 500.0f, "%.0fm");
    }
}

void BF1ModGUI::RenderMiscTab() {
    ImGui::Text("Miscellaneous Settings");
    ImGui::Separator();

    if (ImGui::Button("No Recoil", CONTROLLER_BUTTON_SIZE)) {
        m_settings.noRecoil = !m_settings.noRecoil;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.noRecoil ? "ON" : "OFF");

    if (ImGui::Button("No Spread", CONTROLLER_BUTTON_SIZE)) {
        m_settings.noSpread = !m_settings.noSpread;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.noSpread ? "ON" : "OFF");

    if (ImGui::Button("Rapid Fire", CONTROLLER_BUTTON_SIZE)) {
        m_settings.rapidFire = !m_settings.rapidFire;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.rapidFire ? "ON" : "OFF");

    if (ImGui::Button("Infinite Ammo", CONTROLLER_BUTTON_SIZE)) {
        m_settings.infiniteAmmo = !m_settings.infiniteAmmo;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.infiniteAmmo ? "ON" : "OFF");

    if (ImGui::Button("God Mode", CONTROLLER_BUTTON_SIZE)) {
        m_settings.godMode = !m_settings.godMode;
    }
    ImGui::SameLine();
    ImGui::Text(m_settings.godMode ? "ON" : "OFF");
}

void BF1ModGUI::RenderVisualTab() {
    ImGui::Text("Visual Settings");
    ImGui::Separator();

    ImGui::Text("Menu Opacity");
    ImGui::SliderFloat("##MenuOpacity", &m_settings.menuOpacity, 0.3f, 1.0f, "%.2f");

    ImGui::Text("Menu Theme");
    const char* themes[] = { "Dark", "Light", "Military" };
    if (ImGui::Combo("##MenuTheme", &m_settings.menuTheme, themes, 3)) {
        ApplyTheme(m_settings.menuTheme);
    }

    ImGui::Checkbox("Show FPS", &m_settings.showFPS);

    if (m_settings.showFPS) {
        ImGui::SameLine();
        ImGui::Text("FPS: %.1f", ImGui::GetIO().Framerate);
    }
}

void BF1ModGUI::RenderControllerTab() {
    ImGui::Text("Controller Settings");
    ImGui::Separator();

    // Controller status
    if (m_controller.connected) {
        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Controller Status: Connected");

        // Show controller input values
        ImGui::Text("Left Stick: %.2f, %.2f", m_controller.GetLeftStickX(), m_controller.GetLeftStickY());
        ImGui::Text("Right Stick: %.2f, %.2f", m_controller.GetRightStickX(), m_controller.GetRightStickY());
        ImGui::Text("Triggers: L=%.2f R=%.2f", m_controller.GetLeftTrigger(), m_controller.GetRightTrigger());

        // Button states
        ImGui::Text("Buttons: ");
        ImGui::SameLine();
        if (m_controller.IsButtonHeld(XBOX_A)) ImGui::Text("A ");
        if (m_controller.IsButtonHeld(XBOX_B)) ImGui::Text("B ");
        if (m_controller.IsButtonHeld(XBOX_X)) ImGui::Text("X ");
        if (m_controller.IsButtonHeld(XBOX_Y)) ImGui::Text("Y ");

    } else {
        ImGui::TextColored(ImVec4(1, 0, 0, 1), "Controller Status: Disconnected");
    }

    ImGui::Separator();

    ImGui::Text("Controller Sensitivity");
    ImGui::SliderFloat("##ControllerSens", &m_settings.controllerSensitivity, 0.1f, 3.0f, "%.2f");

    ImGui::Checkbox("Controller Vibration", &m_settings.controllerVibration);

    ImGui::Text("Toggle Menu Key");
    ImGui::Text("Current: INSERT (or START button)");
}

void BF1ModGUI::RenderAboutTab() {
    ImGui::Text("BF1 Mod Menu v1.0");
    ImGui::Separator();

    ImGui::Text("Features:");
    ImGui::BulletText("Xbox Controller Support");
    ImGui::BulletText("Aimbot for Horde Mode");
    ImGui::BulletText("ESP/Wallhacks");
    ImGui::BulletText("Weapon Modifications");
    ImGui::BulletText("Visual Enhancements");

    ImGui::Spacing();
    ImGui::Text("Controls:");
    ImGui::BulletText("START: Toggle Menu");
    ImGui::BulletText("LB/RB: Switch Tabs");
    ImGui::BulletText("D-Pad/Left Stick: Navigate");
    ImGui::BulletText("A: Select/Toggle");
    ImGui::BulletText("B: Back/Cancel");

    ImGui::Spacing();
    ImGui::TextColored(ImVec4(1, 1, 0, 1), "For Horde Mode / Campaign Use Only");
}

void BF1ModGUI::SaveSettings() {
    std::ofstream file("bf1_mod_settings.ini");
    if (!file.is_open()) return;

    file << "[Horde]\n";
    file << "HordeMode=" << m_settings.hordeMode << "\n";
    file << "ShowHordeStats=" << m_settings.showHordeStats << "\n";
    file << "ShowEnemyCount=" << m_settings.showEnemyCount << "\n";
    file << "ShowWaveInfo=" << m_settings.showWaveInfo << "\n";

    file << "[Aimbot]\n";
    file << "Enabled=" << m_settings.aimbotEnabled << "\n";
    file << "FOV=" << m_settings.aimbotFOV << "\n";
    file << "Smooth=" << m_settings.aimbotSmooth << "\n";
    file << "Bone=" << m_settings.aimbotBone << "\n";
    file << "VisibleOnly=" << m_settings.aimbotVisibleOnly << "\n";
    file << "AutoFire=" << m_settings.aimbotAutoFire << "\n";
    file << "Prediction=" << m_settings.aimbotPrediction << "\n";

    file << "[ESP]\n";
    file << "Enabled=" << m_settings.espEnabled << "\n";
    file << "Boxes=" << m_settings.espBoxes << "\n";
    file << "Names=" << m_settings.espNames << "\n";
    file << "Health=" << m_settings.espHealth << "\n";
    file << "Distance=" << m_settings.espDistance << "\n";
    file << "Weapons=" << m_settings.espWeapons << "\n";
    file << "ThreatLevel=" << m_settings.espThreatLevel << "\n";
    file << "MaxDistance=" << m_settings.espMaxDistance << "\n";

    file << "[Weapons]\n";
    file << "NoRecoil=" << m_settings.noRecoil << "\n";
    file << "NoSpread=" << m_settings.noSpread << "\n";
    file << "RapidFire=" << m_settings.rapidFire << "\n";
    file << "InfiniteAmmo=" << m_settings.infiniteAmmo << "\n";
    file << "OneHitKill=" << m_settings.oneHitKill << "\n";
    file << "InstantReload=" << m_settings.instantReload << "\n";
    file << "DamageMultiplier=" << m_settings.damageMultiplier << "\n";

    file << "[Player]\n";
    file << "GodMode=" << m_settings.godMode << "\n";
    file << "SpeedHack=" << m_settings.speedHack << "\n";
    file << "JumpHack=" << m_settings.jumpHack << "\n";
    file << "NoClip=" << m_settings.noClip << "\n";
    file << "SpeedMultiplier=" << m_settings.speedMultiplier << "\n";
    file << "JumpMultiplier=" << m_settings.jumpMultiplier << "\n";

    file << "[HordeSpecific]\n";
    file << "WaveSkip=" << m_settings.waveSkip << "\n";
    file << "EnemyFreeze=" << m_settings.enemyFreeze << "\n";
    file << "AutoRevive=" << m_settings.autoRevive << "\n";
    file << "UnlimitedGrenades=" << m_settings.unlimitedGrenades << "\n";

    file << "[Visual]\n";
    file << "MenuOpacity=" << m_settings.menuOpacity << "\n";
    file << "MenuTheme=" << m_settings.menuTheme << "\n";
    file << "ShowFPS=" << m_settings.showFPS << "\n";
    file << "RemoveFlash=" << m_settings.removeFlash << "\n";
    file << "RemoveSmoke=" << m_settings.removeSmoke << "\n";
    file << "BrightSkins=" << m_settings.brightSkins << "\n";
    file << "CustomFOV=" << m_settings.customFOV << "\n";
    file << "FOVValue=" << m_settings.fovValue << "\n";
    file << "ShowCrosshair=" << m_settings.showCrosshair << "\n";

    file << "[Audio]\n";
    file << "SoundESP=" << m_settings.soundESP << "\n";
    file << "RemoveAmbient=" << m_settings.removeAmbient << "\n";
    file << "MasterVolume=" << m_settings.masterVolume << "\n";

    file << "[Controller]\n";
    file << "Sensitivity=" << m_settings.controllerSensitivity << "\n";
    file << "Vibration=" << m_settings.controllerVibration << "\n";
    file << "ToggleKey=" << m_settings.toggleKey << "\n";

    file.close();
}

void BF1ModGUI::LoadSettings() {
    std::ifstream file("bf1_mod_settings.ini");
    if (!file.is_open()) return;

    std::string line;
    std::string section;

    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;

        if (line[0] == '[') {
            section = line.substr(1, line.find(']') - 1);
            continue;
        }

        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        // Parse settings based on section
        if (section == "Horde") {
            if (key == "HordeMode") m_settings.hordeMode = (value == "1");
            else if (key == "ShowHordeStats") m_settings.showHordeStats = (value == "1");
            else if (key == "ShowEnemyCount") m_settings.showEnemyCount = (value == "1");
            else if (key == "ShowWaveInfo") m_settings.showWaveInfo = (value == "1");
        }
        else if (section == "Aimbot") {
            if (key == "Enabled") m_settings.aimbotEnabled = (value == "1");
            else if (key == "FOV") m_settings.aimbotFOV = std::stof(value);
            else if (key == "Smooth") m_settings.aimbotSmooth = std::stof(value);
            else if (key == "Bone") m_settings.aimbotBone = std::stoi(value);
            else if (key == "VisibleOnly") m_settings.aimbotVisibleOnly = (value == "1");
            else if (key == "AutoFire") m_settings.aimbotAutoFire = (value == "1");
            else if (key == "Prediction") m_settings.aimbotPrediction = (value == "1");
        }
        else if (section == "ESP") {
            if (key == "Enabled") m_settings.espEnabled = (value == "1");
            else if (key == "Boxes") m_settings.espBoxes = (value == "1");
            else if (key == "Names") m_settings.espNames = (value == "1");
            else if (key == "Health") m_settings.espHealth = (value == "1");
            else if (key == "Distance") m_settings.espDistance = (value == "1");
            else if (key == "Weapons") m_settings.espWeapons = (value == "1");
            else if (key == "ThreatLevel") m_settings.espThreatLevel = (value == "1");
            else if (key == "MaxDistance") m_settings.espMaxDistance = std::stof(value);
        }
        else if (section == "Weapons") {
            if (key == "NoRecoil") m_settings.noRecoil = (value == "1");
            else if (key == "NoSpread") m_settings.noSpread = (value == "1");
            else if (key == "RapidFire") m_settings.rapidFire = (value == "1");
            else if (key == "InfiniteAmmo") m_settings.infiniteAmmo = (value == "1");
            else if (key == "OneHitKill") m_settings.oneHitKill = (value == "1");
            else if (key == "InstantReload") m_settings.instantReload = (value == "1");
            else if (key == "DamageMultiplier") m_settings.damageMultiplier = std::stof(value);
        }
        else if (section == "Player") {
            if (key == "GodMode") m_settings.godMode = (value == "1");
            else if (key == "SpeedHack") m_settings.speedHack = (value == "1");
            else if (key == "JumpHack") m_settings.jumpHack = (value == "1");
            else if (key == "NoClip") m_settings.noClip = (value == "1");
            else if (key == "SpeedMultiplier") m_settings.speedMultiplier = std::stof(value);
            else if (key == "JumpMultiplier") m_settings.jumpMultiplier = std::stof(value);
        }
        else if (section == "HordeSpecific") {
            if (key == "WaveSkip") m_settings.waveSkip = (value == "1");
            else if (key == "EnemyFreeze") m_settings.enemyFreeze = (value == "1");
            else if (key == "AutoRevive") m_settings.autoRevive = (value == "1");
            else if (key == "UnlimitedGrenades") m_settings.unlimitedGrenades = (value == "1");
        }
        else if (section == "Visual") {
            if (key == "MenuOpacity") m_settings.menuOpacity = std::stof(value);
            else if (key == "MenuTheme") m_settings.menuTheme = std::stoi(value);
            else if (key == "ShowFPS") m_settings.showFPS = (value == "1");
            else if (key == "RemoveFlash") m_settings.removeFlash = (value == "1");
            else if (key == "RemoveSmoke") m_settings.removeSmoke = (value == "1");
            else if (key == "BrightSkins") m_settings.brightSkins = (value == "1");
            else if (key == "CustomFOV") m_settings.customFOV = (value == "1");
            else if (key == "FOVValue") m_settings.fovValue = std::stof(value);
            else if (key == "ShowCrosshair") m_settings.showCrosshair = (value == "1");
        }
        else if (section == "Audio") {
            if (key == "SoundESP") m_settings.soundESP = (value == "1");
            else if (key == "RemoveAmbient") m_settings.removeAmbient = (value == "1");
            else if (key == "MasterVolume") m_settings.masterVolume = std::stof(value);
        }
        else if (section == "Controller") {
            if (key == "Sensitivity") m_settings.controllerSensitivity = std::stof(value);
            else if (key == "Vibration") m_settings.controllerVibration = (value == "1");
            else if (key == "ToggleKey") m_settings.toggleKey = std::stoi(value);
        }
    }

    file.close();
    ApplyTheme(m_settings.menuTheme);
}
